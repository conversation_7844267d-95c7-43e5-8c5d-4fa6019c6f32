# Dockerfile for Turret Simulation
FROM osrf/ros:noetic-desktop-full

# Install additional dependencies
RUN apt-get update && apt-get install -y \
    ros-noetic-gazebo-ros-pkgs \
    ros-noetic-gazebo-ros-control \
    ros-noetic-controller-manager \
    ros-noetic-joint-state-controller \
    ros-noetic-position-controllers \
    ros-noetic-effort-controllers \
    ros-noetic-image-view \
    ros-noetic-cv-bridge \
    python3-opencv \
    python3-tk \
    x11-apps \
    && rm -rf /var/lib/apt/lists/*

# Create workspace
RUN mkdir -p /catkin_ws/src
WORKDIR /catkin_ws

# Copy the turret simulation package
COPY . /catkin_ws/src/turret_simulation/

# Build the package
RUN /bin/bash -c "source /opt/ros/noetic/setup.bash && catkin_make"

# Setup environment
RUN echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
RUN echo "source /catkin_ws/devel/setup.bash" >> ~/.bashrc

# Make scripts executable
RUN chmod +x /catkin_ws/src/turret_simulation/scripts/*.py

# Set the default command
CMD ["/bin/bash", "-c", "source /catkin_ws/devel/setup.bash && roslaunch turret_simulation turret_complete.launch"]
