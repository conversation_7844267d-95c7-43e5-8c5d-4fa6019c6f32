# Military Turret Simulation

A complete ROS/Gazebo simulation of a military turret system with dual cameras, servo control, and advanced targeting capabilities.

## System Components

Based on the provided diagram, this simulation includes:

- **Fixed Base**: Stable platform foundation
- **Rotating Turret Base**: 360° horizontal rotation capability
- **Gun Mount/Module**: Vertical elevation control
- **Main Camera**: High-resolution primary vision system
- **Thermal/IR Camera**: Secondary thermal imaging system
- **Electronic Speed Controller (ESC)**: Motor control system
- **Main Battery Pack**: Power management system
- **Turret Servo Motor**: Precision positioning control

## Features

### Control Modes
- **Manual Control**: Direct operator control via GUI or keyboard
- **Auto Track**: Automatic target tracking using computer vision
- **Patrol Mode**: Automated scanning pattern with predefined waypoints

### Camera Systems
- **Main Camera**: 1920x1080 RGB camera with image enhancement
- **Thermal Camera**: 640x480 thermal imaging with heat signature detection
- **Combined View**: Side-by-side camera display
- **Real-time Processing**: Object detection and tracking capabilities

### Advanced Capabilities
- **Target Detection**: Color-based and thermal signature detection
- **Position Control**: Precise servo motor positioning
- **Safety Systems**: Emergency stop and limit enforcement
- **Status Monitoring**: Real-time system status and diagnostics

## Installation

### Prerequisites
```bash
# ROS Noetic (Ubuntu 20.04) or ROS Melodic (Ubuntu 18.04)
sudo apt update
sudo apt install ros-noetic-desktop-full  # or ros-melodic-desktop-full

# Required packages
sudo apt install ros-noetic-gazebo-ros-pkgs ros-noetic-gazebo-ros-control
sudo apt install ros-noetic-controller-manager ros-noetic-joint-state-controller
sudo apt install ros-noetic-position-controllers ros-noetic-effort-controllers
sudo apt install ros-noetic-image-view ros-noetic-cv-bridge
sudo apt install python3-opencv python3-tk
```

### Build the Package
```bash
# Create workspace (if not exists)
mkdir -p ~/catkin_ws/src
cd ~/catkin_ws/src

# Clone or copy this package
cp -r /path/to/turret_simulation .

# Build
cd ~/catkin_ws
catkin_make

# Source the workspace
source devel/setup.bash
echo "source ~/catkin_ws/devel/setup.bash" >> ~/.bashrc
```

## Usage

### Quick Start
```bash
# Launch complete simulation with UI
roslaunch turret_simulation turret_complete.launch
```

This will start:
- Gazebo simulation with turret model
- Control system nodes
- Camera processing
- GUI control interface
- Camera view windows
- RViz 3D visualization

### Individual Components

#### Simulation Only
```bash
roslaunch turret_simulation turret_simulation.launch
```

#### Control UI Only
```bash
roslaunch turret_simulation turret_ui.launch
```

### Manual Control

#### Using GUI
1. Launch the complete simulation
2. Use the GUI control panel:
   - **Direction buttons**: Move turret manually
   - **Speed slider**: Adjust movement speed
   - **Mode selection**: Switch between control modes
   - **Direct position**: Set specific angles
   - **Emergency stop**: Immediate halt

#### Using Command Line
```bash
# Manual movement commands
rostopic pub /turret/cmd_vel geometry_msgs/Twist "linear: {x: 0.1, y: 0, z: 0}, angular: {x: 0, y: 0, z: 0.2}"

# Direct position control
rostopic pub /turret/target_position geometry_msgs/Twist "linear: {x: 0.3, y: 0, z: 0}, angular: {x: 0, y: 0, z: 1.57}"

# Change control mode
rostopic pub /turret/set_mode std_msgs/String "data: 'patrol'"
```

### Camera Control
```bash
# Enable/disable detection
rostopic pub /turret/enable_detection std_msgs/Bool "data: true"

# Change processing mode
rostopic pub /turret/processing_mode std_msgs/String "data: 'enhanced'"
```

## Control Modes

### Manual Mode
- Direct operator control
- Real-time response to commands
- Safety limits enforced

### Auto Track Mode
- Automatic target detection and tracking
- Color-based object recognition
- Thermal signature tracking
- Proportional control for smooth tracking

### Patrol Mode
- Automated scanning pattern
- Configurable waypoints
- Timed movements between positions
- Continuous surveillance capability

## Topics and Services

### Published Topics
- `/turret/joint_states` - Current joint positions
- `/turret/status` - System status messages
- `/turret/main_camera/image_raw` - Main camera feed
- `/turret/thermal_camera/image_raw` - Thermal camera feed
- `/turret/combined_view` - Combined camera display
- `/turret/detections` - Target detection results
- `/turret/target_count` - Number of detected targets

### Subscribed Topics
- `/turret/cmd_vel` - Manual movement commands
- `/turret/target_position` - Direct position commands
- `/turret/set_mode` - Control mode changes
- `/turret/enable_detection` - Enable/disable detection
- `/turret/processing_mode` - Camera processing mode

## Configuration

### Control Parameters
Edit `config/turret_control.yaml` to modify:
- PID controller gains
- Movement limits
- Camera settings
- Patrol waypoints

### Physical Parameters
Edit `urdf/turret.urdf.xacro` to modify:
- Link dimensions and masses
- Joint limits and dynamics
- Camera positions
- Material properties

## Troubleshooting

### Common Issues

1. **Gazebo crashes on startup**
   ```bash
   killall gzserver gzclient
   roslaunch turret_simulation turret_simulation.launch
   ```

2. **Controllers not loading**
   ```bash
   # Check controller status
   rosservice call /turret/controller_manager/list_controllers
   
   # Restart controllers
   rosservice call /turret/controller_manager/reload_controller_libraries
   ```

3. **Camera feeds not showing**
   ```bash
   # Check camera topics
   rostopic list | grep camera
   rostopic hz /turret/main_camera/image_raw
   ```

4. **GUI not responding**
   ```bash
   # Restart UI
   rosnode kill /turret_ui
   rosrun turret_simulation turret_ui.py
   ```

### Performance Optimization
- Reduce camera resolution in URDF for better performance
- Adjust physics update rate in world file
- Disable unnecessary visual elements in Gazebo

## Development

### Adding New Features
1. **New Control Modes**: Extend `turret_controller.py`
2. **Enhanced Detection**: Modify `camera_manager.py`
3. **Additional Sensors**: Add to URDF and create processing nodes
4. **Custom Behaviors**: Implement new ROS nodes

### Testing
```bash
# Run system tests
rostest turret_simulation turret_tests.test

# Monitor system performance
rostopic hz /turret/joint_states
rostopic bw /turret/main_camera/image_raw
```

## License
MIT License - See LICENSE file for details

## Support
For issues and questions, please check the troubleshooting section or create an issue in the repository.
