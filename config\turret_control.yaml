# Turret Control Configuration

turret:
  # Joint State Controller
  joint_state_controller:
    type: joint_state_controller/JointStateController
    publish_rate: 50

  # Turret Rotation Controller
  turret_rotation_controller:
    type: position_controllers/JointPositionController
    joint: turret_rotation
    pid: {p: 1000.0, i: 0.01, d: 100.0}

  # Gun Elevation Controller  
  gun_elevation_controller:
    type: position_controllers/JointPositionController
    joint: gun_elevation
    pid: {p: 800.0, i: 0.01, d: 80.0}

# Turret Parameters
turret_params:
  # Rotation limits (radians)
  rotation_min: -3.14159
  rotation_max: 3.14159
  
  # Elevation limits (radians)
  elevation_min: -0.5
  elevation_max: 0.8
  
  # Maximum velocities (rad/s)
  max_rotation_velocity: 1.57
  max_elevation_velocity: 1.0
  
  # Camera parameters
  main_camera:
    topic: "/turret/main_camera/image_raw"
    info_topic: "/turret/main_camera/camera_info"
    frame_id: "main_camera"
    
  thermal_camera:
    topic: "/turret/thermal_camera/image_raw"
    info_topic: "/turret/thermal_camera/camera_info"
    frame_id: "thermal_camera"

# Control modes
control_modes:
  manual: true
  auto_track: false
  patrol: false
  
# Patrol settings
patrol:
  waypoints:
    - {rotation: 0.0, elevation: 0.0, duration: 3.0}
    - {rotation: 1.57, elevation: 0.2, duration: 3.0}
    - {rotation: 3.14, elevation: 0.0, duration: 3.0}
    - {rotation: -1.57, elevation: 0.2, duration: 3.0}
  cycle_time: 20.0
