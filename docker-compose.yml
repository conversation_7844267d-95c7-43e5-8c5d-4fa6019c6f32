version: '3.8'

services:
  turret-simulation:
    build: .
    container_name: turret_sim
    environment:
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - .:/catkin_ws/src/turret_simulation
    network_mode: host
    stdin_open: true
    tty: true
    command: >
      bash -c "source /catkin_ws/devel/setup.bash && 
               roslaunch turret_simulation turret_complete.launch"

  turret-gazebo-only:
    build: .
    container_name: turret_gazebo
    environment:
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - .:/catkin_ws/src/turret_simulation
    network_mode: host
    stdin_open: true
    tty: true
    command: >
      bash -c "source /catkin_ws/devel/setup.bash && 
               roslaunch turret_simulation turret_simulation.launch"

  turret-ui-only:
    build: .
    container_name: turret_ui
    environment:
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - .:/catkin_ws/src/turret_simulation
    network_mode: host
    stdin_open: true
    tty: true
    command: >
      bash -c "source /catkin_ws/devel/setup.bash && 
               roslaunch turret_simulation turret_ui.launch"
