<?xml version="1.0"?>
<launch>
  <!-- Complete turret simulation with UI -->
  
  <!-- Launch the main simulation -->
  <include file="$(find turret_simulation)/launch/turret_simulation.launch">
    <arg name="world_name" value="turret_range"/>
    <arg name="gui" value="true"/>
  </include>

  <!-- Wait a bit for simulation to start -->
  <arg name="delay" default="3"/>
  
  <!-- Launch UI after delay -->
  <group>
    <!-- Turret control UI -->
    <node name="turret_ui" pkg="turret_simulation" type="turret_ui.py" 
          output="screen" respawn="false" launch-prefix="bash -c 'sleep $(arg delay); $0 $@'">
      <param name="window_title" value="Military Turret Control System"/>
    </node>

    <!-- Camera views -->
    <node name="main_camera_view" pkg="image_view" type="image_view" respawn="false"
          launch-prefix="bash -c 'sleep $(arg delay); $0 $@'">
      <remap from="image" to="/turret/main_camera/image_raw"/>
      <param name="window_name" value="Main Camera"/>
    </node>

    <node name="thermal_camera_view" pkg="image_view" type="image_view" respawn="false"
          launch-prefix="bash -c 'sleep $(arg delay); $0 $@'">
      <remap from="image" to="/turret/thermal_camera/image_raw"/>
      <param name="window_name" value="Thermal Camera"/>
    </node>

    <node name="combined_view" pkg="image_view" type="image_view" respawn="false"
          launch-prefix="bash -c 'sleep $(arg delay); $0 $@'">
      <remap from="image" to="/turret/combined_view"/>
      <param name="window_name" value="Combined Camera View"/>
    </node>
  </group>

  <!-- Optional: Launch RViz for 3D visualization -->
  <node name="rviz" pkg="rviz" type="rviz" respawn="false"
        args="-d $(find turret_simulation)/rviz/turret_config.rviz"
        launch-prefix="bash -c 'sleep $(arg delay); $0 $@'"/>

</launch>
