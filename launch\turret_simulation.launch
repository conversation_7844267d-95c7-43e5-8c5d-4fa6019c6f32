<?xml version="1.0"?>
<launch>
  <!-- Arguments -->
  <arg name="world_name" default="turret_range"/>
  <arg name="paused" default="false"/>
  <arg name="use_sim_time" default="true"/>
  <arg name="gui" default="true"/>
  <arg name="headless" default="false"/>
  <arg name="debug" default="false"/>
  <arg name="verbose" default="false"/>
  <arg name="spawn_x" default="0"/>
  <arg name="spawn_y" default="0"/>
  <arg name="spawn_z" default="0"/>

  <!-- Set use_sim_time parameter -->
  <param name="use_sim_time" value="$(arg use_sim_time)"/>

  <!-- Start Gazebo with the turret world -->
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(find turret_simulation)/worlds/$(arg world_name).world"/>
    <arg name="paused" value="$(arg paused)"/>
    <arg name="use_sim_time" value="$(arg use_sim_time)"/>
    <arg name="gui" value="$(arg gui)"/>
    <arg name="headless" value="$(arg headless)"/>
    <arg name="debug" value="$(arg debug)"/>
    <arg name="verbose" value="$(arg verbose)"/>
  </include>

  <!-- Load robot description -->
  <param name="robot_description" command="$(find xacro)/xacro $(find turret_simulation)/urdf/turret.urdf.xacro"/>

  <!-- Spawn the turret in Gazebo -->
  <node name="spawn_turret" pkg="gazebo_ros" type="spawn_model" 
        args="-urdf -model military_turret -param robot_description 
              -x $(arg spawn_x) -y $(arg spawn_y) -z $(arg spawn_z)"
        output="screen"/>

  <!-- Robot state publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher">
    <param name="publish_frequency" type="double" value="50.0"/>
  </node>

  <!-- Joint state publisher -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <param name="use_gui" value="false"/>
  </node>

  <!-- Load controller configuration -->
  <rosparam file="$(find turret_simulation)/config/turret_control.yaml" command="load"/>

  <!-- Controller manager -->
  <node name="controller_spawner" pkg="controller_manager" type="spawner" respawn="false"
        output="screen" ns="/turret" 
        args="joint_state_controller
              turret_rotation_controller
              gun_elevation_controller"/>

  <!-- Turret controller node -->
  <node name="turret_controller" pkg="turret_simulation" type="turret_controller.py" 
        output="screen" respawn="true">
    <remap from="/turret/joint_states" to="/turret/joint_states"/>
  </node>

  <!-- Camera manager node -->
  <node name="camera_manager" pkg="turret_simulation" type="camera_manager.py" 
        output="screen" respawn="true"/>

  <!-- TF static transforms -->
  <node pkg="tf2_ros" type="static_transform_publisher" name="world_to_base_link"
        args="0 0 0 0 0 0 world base_link"/>

</launch>
