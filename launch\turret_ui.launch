<?xml version="1.0"?>
<launch>
  <!-- Launch the turret UI -->
  <node name="turret_ui" pkg="turret_simulation" type="turret_ui.py" 
        output="screen" respawn="false">
    <param name="window_title" value="Military Turret Control System"/>
  </node>

  <!-- Launch image view for camera feeds -->
  <node name="main_camera_view" pkg="image_view" type="image_view" respawn="false">
    <remap from="image" to="/turret/main_camera/image_raw"/>
    <param name="window_name" value="Main Camera"/>
  </node>

  <node name="thermal_camera_view" pkg="image_view" type="image_view" respawn="false">
    <remap from="image" to="/turret/thermal_camera/image_raw"/>
    <param name="window_name" value="Thermal Camera"/>
  </node>

  <node name="combined_view" pkg="image_view" type="image_view" respawn="false">
    <remap from="image" to="/turret/combined_view"/>
    <param name="window_name" value="Combined Camera View"/>
  </node>

</launch>
