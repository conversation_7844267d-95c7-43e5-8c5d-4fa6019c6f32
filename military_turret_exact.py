#!/usr/bin/env python3
"""
Military Turret System - Exact Replica
شبیه‌سازی دقیق سیستم توپ نظامی مطابق تصویر اصلی
"""

import tkinter as tk
from tkinter import ttk, messagebox
import math
import time
import threading
import random
import pygame
from PIL import Image, ImageTk, ImageDraw

class MilitaryTurretExact:
    def __init__(self):
        # Initialize pygame for sound
        pygame.mixer.init()
        
        self.root = tk.Tk()
        self.root.title("🎯 Military Turret Control System - Exact Replica")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#1a1a1a')
        
        # System state exactly like the original diagram
        self.rotation = 0.0
        self.elevation = 0.0
        self.target_rotation = 0.0
        self.target_elevation = 0.0
        
        # Components from original diagram
        self.fixed_base_active = True
        self.turret_servo_active = True
        self.esc_active = True
        self.main_camera_active = True
        self.thermal_camera_active = True
        self.battery_level = 100
        self.main_battery_voltage = 24.0
        
        # Firing system
        self.ammo_count = 100
        self.firing_ready = True
        self.auto_fire = False
        self.target_locked = False
        
        # Real-time data
        self.system_temperature = 25
        self.servo_current = 0.0
        self.gun_elevation_angle = 0.0
        self.turret_rotation_angle = 0.0
        
        # Targets and tracking
        self.targets = []
        self.active_target = None
        self.tracking_mode = False
        
        self.create_exact_interface()
        self.generate_battlefield_targets()
        self.start_system_thread()
        
    def create_exact_interface(self):
        """Create interface exactly like the original military system"""
        
        # Main container with military dark theme
        main_container = tk.Frame(self.root, bg='#0a0a0a', relief='raised', bd=3)
        main_container.pack(fill='both', expand=True, padx=3, pady=3)
        
        # Top status bar - exactly like military systems
        top_bar = tk.Frame(main_container, bg='#1a2a1a', height=40, relief='raised', bd=2)
        top_bar.pack(fill='x', pady=(0, 3))
        top_bar.pack_propagate(False)
        
        # System title
        title_label = tk.Label(top_bar, text="🎯 MILITARY TURRET CONTROL SYSTEM - OPERATIONAL", 
                              font=('Courier New', 12, 'bold'), bg='#1a2a1a', fg='#00ff00')
        title_label.pack(side='left', padx=10, pady=8)
        
        # System time and status
        self.system_time = tk.StringVar()
        time_label = tk.Label(top_bar, textvariable=self.system_time, 
                             font=('Courier New', 10), bg='#1a2a1a', fg='#ffff00')
        time_label.pack(side='right', padx=10, pady=8)
        
        # Main control area
        control_area = tk.Frame(main_container, bg='#0a0a0a')
        control_area.pack(fill='both', expand=True)
        
        # Left panel - System components (like original diagram)
        left_panel = tk.Frame(control_area, bg='#1a1a1a', width=350, relief='raised', bd=2)
        left_panel.pack(side='left', fill='y', padx=(0, 3))
        left_panel.pack_propagate(False)
        
        self.create_system_components_panel(left_panel)
        
        # Center panel - Main display and turret view
        center_panel = tk.Frame(control_area, bg='#1a1a1a', relief='raised', bd=2)
        center_panel.pack(side='left', fill='both', expand=True, padx=3)
        
        self.create_main_display_panel(center_panel)
        
        # Right panel - Camera feeds and targeting
        right_panel = tk.Frame(control_area, bg='#1a1a1a', width=400, relief='raised', bd=2)
        right_panel.pack(side='right', fill='y', padx=(3, 0))
        right_panel.pack_propagate(False)
        
        self.create_camera_targeting_panel(right_panel)
        
        # Bottom control bar
        bottom_bar = tk.Frame(main_container, bg='#2a2a2a', height=60, relief='raised', bd=2)
        bottom_bar.pack(fill='x', pady=(3, 0))
        bottom_bar.pack_propagate(False)
        
        self.create_bottom_controls(bottom_bar)
        
    def create_system_components_panel(self, parent):
        """Create system components panel exactly like the diagram"""
        
        # Title
        title = tk.Label(parent, text="🔧 SYSTEM COMPONENTS", 
                        font=('Courier New', 11, 'bold'), bg='#1a1a1a', fg='#00ff00')
        title.pack(pady=10)
        
        # Fixed Base status
        base_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        base_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(base_frame, text="🏗️ FIXED BASE", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.base_status = tk.StringVar(value="✅ OPERATIONAL")
        tk.Label(base_frame, textvariable=self.base_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        # Turret Servo Motor
        servo_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        servo_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(servo_frame, text="⚙️ TURRET SERVO MOTOR", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.servo_status = tk.StringVar(value="✅ ACTIVE")
        tk.Label(servo_frame, textvariable=self.servo_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        self.servo_current_var = tk.StringVar(value="Current: 0.0A")
        tk.Label(servo_frame, textvariable=self.servo_current_var, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#ffffff').pack(anchor='w', padx=15, pady=1)
        
        # Electronic Speed Controller (ESC)
        esc_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        esc_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(esc_frame, text="🔌 ELECTRONIC SPEED CONTROLLER", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.esc_status = tk.StringVar(value="✅ ONLINE")
        tk.Label(esc_frame, textvariable=self.esc_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        # Main Battery Pack
        battery_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        battery_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(battery_frame, text="🔋 MAIN BATTERY PACK", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.battery_status = tk.StringVar(value="✅ 100% CHARGED")
        tk.Label(battery_frame, textvariable=self.battery_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        self.voltage_var = tk.StringVar(value="Voltage: 24.0V")
        tk.Label(battery_frame, textvariable=self.voltage_var, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#ffffff').pack(anchor='w', padx=15, pady=1)
        
        # Battery level bar
        self.battery_progress = ttk.Progressbar(battery_frame, length=200, mode='determinate')
        self.battery_progress.pack(padx=15, pady=5)
        self.battery_progress['value'] = 100
        
        # Main Camera
        main_cam_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        main_cam_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(main_cam_frame, text="📹 MAIN CAMERA", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.main_cam_status = tk.StringVar(value="✅ RECORDING")
        tk.Label(main_cam_frame, textvariable=self.main_cam_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        # Thermal/IR Camera
        thermal_cam_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        thermal_cam_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(thermal_cam_frame, text="🌡️ THERMAL/IR CAMERA", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.thermal_cam_status = tk.StringVar(value="✅ ACTIVE")
        tk.Label(thermal_cam_frame, textvariable=self.thermal_cam_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        # Gun Mount/Module
        gun_frame = tk.Frame(parent, bg='#2a2a2a', relief='raised', bd=1)
        gun_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(gun_frame, text="🔫 GUN MOUNT/MODULE", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack(anchor='w', padx=5, pady=2)
        
        self.gun_status = tk.StringVar(value="✅ READY TO FIRE")
        tk.Label(gun_frame, textvariable=self.gun_status, font=('Courier New', 8), 
                bg='#2a2a2a', fg='#00ff00').pack(anchor='w', padx=15, pady=2)
        
        self.ammo_status = tk.StringVar(value="Ammo: 100/100")
        tk.Label(gun_frame, textvariable=self.ammo_status, font=('Courier New', 8),
                bg='#2a2a2a', fg='#ffffff').pack(anchor='w', padx=15, pady=1)

    def create_main_display_panel(self, parent):
        """Create main display panel with turret visualization"""

        # Title
        title = tk.Label(parent, text="🎯 TURRET CONTROL & BATTLEFIELD VIEW",
                        font=('Courier New', 11, 'bold'), bg='#1a1a1a', fg='#00ff00')
        title.pack(pady=10)

        # Main battlefield display
        battlefield_frame = tk.Frame(parent, bg='#000000', relief='sunken', bd=3)
        battlefield_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.battlefield_canvas = tk.Canvas(battlefield_frame, bg='#001100',
                                          width=800, height=500, relief='sunken', bd=2)
        self.battlefield_canvas.pack(pady=5)

        # Control panel below battlefield
        control_panel = tk.Frame(parent, bg='#2a2a2a', height=150, relief='raised', bd=2)
        control_panel.pack(fill='x', padx=10, pady=5)
        control_panel.pack_propagate(False)

        # Position display (exactly like your image)
        pos_frame = tk.Frame(control_panel, bg='#000000', relief='sunken', bd=2)
        pos_frame.pack(side='left', fill='y', padx=5, pady=5)

        # Rotation display
        tk.Label(pos_frame, text="ROT:", font=('Courier New', 10, 'bold'),
                bg='#000000', fg='#00ff00').pack(anchor='w', padx=5, pady=2)

        self.rotation_display = tk.StringVar(value="-30.0°")
        tk.Label(pos_frame, textvariable=self.rotation_display, font=('Courier New', 12, 'bold'),
                bg='#000000', fg='#00ff00').pack(anchor='w', padx=15, pady=2)

        # Elevation display
        tk.Label(pos_frame, text="ELEV:", font=('Courier New', 10, 'bold'),
                bg='#000000', fg='#00ff00').pack(anchor='w', padx=5, pady=2)

        self.elevation_display = tk.StringVar(value="70.0°")
        tk.Label(pos_frame, textvariable=self.elevation_display, font=('Courier New', 12, 'bold'),
                bg='#000000', fg='#00ff00').pack(anchor='w', padx=15, pady=2)

        # Status indicator (like your image)
        tk.Label(pos_frame, text="✅ READY", font=('Courier New', 10, 'bold'),
                bg='#000000', fg='#00ff00').pack(anchor='w', padx=5, pady=10)

        # Turret visualization (exactly like your circular display)
        turret_frame = tk.Frame(control_panel, bg='#000000', relief='sunken', bd=2)
        turret_frame.pack(side='left', fill='y', padx=5, pady=5)

        self.turret_canvas = tk.Canvas(turret_frame, bg='#000000', width=200, height=130)
        self.turret_canvas.pack(padx=5, pady=5)

        # Manual controls
        manual_frame = tk.Frame(control_panel, bg='#2a2a2a')
        manual_frame.pack(side='right', fill='y', padx=5, pady=5)

        tk.Label(manual_frame, text="MANUAL CONTROL", font=('Courier New', 9, 'bold'),
                bg='#2a2a2a', fg='#ffff00').pack(pady=5)

        # Direction buttons
        btn_frame = tk.Frame(manual_frame, bg='#2a2a2a')
        btn_frame.pack()

        tk.Button(btn_frame, text="▲", font=('Courier New', 12, 'bold'),
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(0, 5)).grid(row=0, column=1, padx=1, pady=1)

        tk.Button(btn_frame, text="◄", font=('Courier New', 12, 'bold'),
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(-10, 0)).grid(row=1, column=0, padx=1, pady=1)

        tk.Button(btn_frame, text="🔥", font=('Courier New', 10, 'bold'),
                 bg='#ff0000', fg='white', width=3, height=1,
                 command=self.fire_weapon).grid(row=1, column=1, padx=1, pady=1)

        tk.Button(btn_frame, text="►", font=('Courier New', 12, 'bold'),
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(10, 0)).grid(row=1, column=2, padx=1, pady=1)

        tk.Button(btn_frame, text="▼", font=('Courier New', 12, 'bold'),
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(0, -5)).grid(row=2, column=1, padx=1, pady=1)

    def create_camera_targeting_panel(self, parent):
        """Create camera feeds and targeting system"""

        # Title
        title = tk.Label(parent, text="📹 CAMERA SYSTEMS & TARGETING",
                        font=('Courier New', 11, 'bold'), bg='#1a1a1a', fg='#00ff00')
        title.pack(pady=10)

        # Main camera feed
        main_cam_frame = tk.LabelFrame(parent, text="📹 MAIN CAMERA",
                                      font=('Courier New', 9, 'bold'),
                                      bg='#1a1a1a', fg='#00ff00', relief='raised', bd=2)
        main_cam_frame.pack(fill='x', padx=10, pady=5)

        self.main_camera_canvas = tk.Canvas(main_cam_frame, bg='#003300',
                                          width=360, height=200, relief='sunken', bd=2)
        self.main_camera_canvas.pack(pady=5)

        # Thermal camera feed
        thermal_cam_frame = tk.LabelFrame(parent, text="🌡️ THERMAL/IR CAMERA",
                                         font=('Courier New', 9, 'bold'),
                                         bg='#1a1a1a', fg='#ff6600', relief='raised', bd=2)
        thermal_cam_frame.pack(fill='x', padx=10, pady=5)

        self.thermal_camera_canvas = tk.Canvas(thermal_cam_frame, bg='#330000',
                                             width=360, height=200, relief='sunken', bd=2)
        self.thermal_camera_canvas.pack(pady=5)

        # Targeting controls
        targeting_frame = tk.LabelFrame(parent, text="🎯 TARGETING SYSTEM",
                                       font=('Courier New', 9, 'bold'),
                                       bg='#1a1a1a', fg='#ffff00', relief='raised', bd=2)
        targeting_frame.pack(fill='x', padx=10, pady=5)

        # Target lock status
        self.target_lock_status = tk.StringVar(value="🔍 SCANNING...")
        tk.Label(targeting_frame, textvariable=self.target_lock_status,
                font=('Courier New', 10, 'bold'), bg='#1a1a1a', fg='#ffff00').pack(pady=5)

        # Auto tracking toggle
        self.auto_track_var = tk.BooleanVar()
        tk.Checkbutton(targeting_frame, text="AUTO TRACKING", variable=self.auto_track_var,
                      font=('Courier New', 9, 'bold'), bg='#1a1a1a', fg='#00ff00',
                      selectcolor='#ff6600', command=self.toggle_auto_tracking).pack(pady=5)

        # Fire control
        fire_frame = tk.Frame(targeting_frame, bg='#1a1a1a')
        fire_frame.pack(fill='x', pady=10)

        self.fire_button = tk.Button(fire_frame, text="🔥 ENGAGE TARGET 🔥",
                                    font=('Courier New', 12, 'bold'),
                                    bg='#ff0000', fg='white', relief='raised', bd=3,
                                    command=self.fire_weapon, width=20, height=2)
        self.fire_button.pack()

    def create_bottom_controls(self, parent):
        """Create bottom control bar"""

        # Emergency controls
        emergency_frame = tk.Frame(parent, bg='#2a2a2a')
        emergency_frame.pack(side='left', fill='y', padx=10)

        tk.Button(emergency_frame, text="🚨 EMERGENCY STOP 🚨",
                 font=('Courier New', 10, 'bold'),
                 bg='#cc0000', fg='white', relief='raised', bd=3,
                 command=self.emergency_stop, width=20).pack(pady=15)

        # System status
        status_frame = tk.Frame(parent, bg='#2a2a2a')
        status_frame.pack(side='right', fill='y', padx=10)

        self.system_status_var = tk.StringVar(value="🟢 ALL SYSTEMS OPERATIONAL")
        tk.Label(status_frame, textvariable=self.system_status_var,
                font=('Courier New', 10, 'bold'), bg='#2a2a2a', fg='#00ff00').pack(pady=20)

    def generate_battlefield_targets(self):
        """Generate targets on battlefield"""
        self.targets = []
        for i in range(random.randint(4, 8)):
            target = {
                'x': random.randint(100, 700),
                'y': random.randint(100, 400),
                'type': random.choice(['tank', 'vehicle', 'building', 'personnel']),
                'health': random.randint(1, 3),
                'destroyed': False,
                'thermal_signature': random.randint(50, 100)
            }
            self.targets.append(target)

    def start_system_thread(self):
        """Start main system thread"""
        self.system_thread = threading.Thread(target=self.system_loop, daemon=True)
        self.system_thread.start()

    def system_loop(self):
        """Main system loop"""
        while True:
            try:
                current_time = time.time()

                # Update system time
                time_str = time.strftime("%H:%M:%S", time.localtime())
                self.system_time.set(f"SYSTEM TIME: {time_str}")

                # Update turret position
                self.update_turret_position()

                # Update system status
                self.update_system_status()

                # Auto tracking
                if self.auto_track_var.get():
                    self.auto_track_targets()

                # Update GUI
                self.root.after(0, self.update_displays)

                time.sleep(0.05)  # 20 Hz

            except Exception as e:
                print(f"System error: {e}")
                time.sleep(0.1)

    def update_turret_position(self):
        """Update turret position with realistic movement"""
        # Smooth movement towards target
        rot_diff = self.target_rotation - self.rotation
        if abs(rot_diff) > 0.1:
            move_speed = min(abs(rot_diff), 2.0)
            if rot_diff > 0:
                self.rotation += move_speed
            else:
                self.rotation -= move_speed

        elev_diff = self.target_elevation - self.elevation
        if abs(elev_diff) > 0.1:
            move_speed = min(abs(elev_diff), 1.5)
            if elev_diff > 0:
                self.elevation += move_speed
            else:
                self.elevation -= move_speed

        # Apply limits
        self.rotation = max(-180, min(180, self.rotation))
        self.elevation = max(-10, min(85, self.elevation))

        # Update servo current based on movement
        movement = abs(rot_diff) + abs(elev_diff)
        self.servo_current = min(5.0, movement * 0.5)

    def update_system_status(self):
        """Update all system status indicators"""
        # Update position displays
        self.rotation_display.set(f"{self.rotation:.1f}°")
        self.elevation_display.set(f"{self.elevation:.1f}°")

        # Update servo status
        self.servo_current_var.set(f"Current: {self.servo_current:.1f}A")

        # Update battery (slowly drain)
        if self.battery_level > 0:
            self.battery_level -= 0.01
            self.battery_progress['value'] = self.battery_level

        if self.battery_level > 80:
            self.battery_status.set("✅ FULLY CHARGED")
        elif self.battery_level > 50:
            self.battery_status.set("⚠️ GOOD CHARGE")
        elif self.battery_level > 20:
            self.battery_status.set("⚠️ LOW BATTERY")
        else:
            self.battery_status.set("🔴 CRITICAL BATTERY")

        # Update voltage
        voltage = 24.0 * (self.battery_level / 100)
        self.voltage_var.set(f"Voltage: {voltage:.1f}V")

        # Update ammo status
        self.ammo_status.set(f"Ammo: {self.ammo_count}/100")

    def auto_track_targets(self):
        """Auto tracking system"""
        if not self.targets:
            return

        # Find closest target
        closest_target = None
        min_distance = float('inf')

        for target in self.targets:
            if target['destroyed']:
                continue

            distance = math.sqrt((target['x'] - 400)**2 + (target['y'] - 250)**2)
            if distance < min_distance:
                min_distance = distance
                closest_target = target

        if closest_target:
            # Calculate angle to target
            dx = closest_target['x'] - 400
            dy = closest_target['y'] - 250

            target_angle = math.degrees(math.atan2(dy, dx))
            target_elevation = math.degrees(math.atan2(dy, math.sqrt(dx**2 + dy**2)))

            self.target_rotation = target_angle
            self.target_elevation = max(-10, min(85, target_elevation))

            self.active_target = closest_target
            self.target_lock_status.set("🎯 TARGET LOCKED")
        else:
            self.target_lock_status.set("🔍 SCANNING...")

    def manual_move(self, rot_delta, elev_delta):
        """Manual turret movement"""
        self.target_rotation = max(-180, min(180, self.rotation + rot_delta))
        self.target_elevation = max(-10, min(85, self.elevation + elev_delta))

    def fire_weapon(self):
        """Fire weapon system"""
        if self.ammo_count <= 0:
            messagebox.showwarning("No Ammo", "⚠️ OUT OF AMMUNITION")
            return

        if not self.firing_ready:
            return

        # Reduce ammo
        self.ammo_count -= 1

        # Create muzzle flash effect
        self.create_muzzle_flash()

        # Check for target hit
        if self.active_target and not self.active_target['destroyed']:
            # Calculate hit probability based on distance and accuracy
            hit_chance = random.random()
            if hit_chance > 0.2:  # 80% hit chance
                self.active_target['health'] -= 1
                if self.active_target['health'] <= 0:
                    self.active_target['destroyed'] = True
                    self.target_lock_status.set("🎯 TARGET DESTROYED")

        # Sound effect
        print("💥 WEAPON FIRED!")

        # Brief firing delay
        self.firing_ready = False
        self.root.after(500, lambda: setattr(self, 'firing_ready', True))

    def create_muzzle_flash(self):
        """Create muzzle flash visual effect"""
        # This will be handled in the display update
        self.muzzle_flash_time = time.time()

    def toggle_auto_tracking(self):
        """Toggle auto tracking mode"""
        if self.auto_track_var.get():
            self.target_lock_status.set("🔍 AUTO TRACKING ENABLED")
        else:
            self.target_lock_status.set("🔍 MANUAL MODE")
            self.active_target = None

    def emergency_stop(self):
        """Emergency stop all systems"""
        self.target_rotation = self.rotation
        self.target_elevation = self.elevation
        self.auto_track_var.set(False)
        self.active_target = None
        messagebox.showwarning("Emergency Stop", "🚨 ALL SYSTEMS HALTED 🚨")

    def update_displays(self):
        """Update all visual displays"""
        self.draw_battlefield()
        self.draw_turret_display()
        self.draw_main_camera()
        self.draw_thermal_camera()

    def draw_battlefield(self):
        """Draw battlefield with targets and effects"""
        self.battlefield_canvas.delete("all")

        # Grid background
        for i in range(0, 800, 50):
            self.battlefield_canvas.create_line(i, 0, i, 500, fill='#003300', width=1)
        for i in range(0, 500, 50):
            self.battlefield_canvas.create_line(0, i, 800, i, fill='#003300', width=1)

        # Draw turret position
        turret_x, turret_y = 400, 250
        self.battlefield_canvas.create_oval(turret_x-8, turret_y-8, turret_x+8, turret_y+8,
                                          fill='#00ff00', outline='white', width=2)

        # Draw turret direction
        angle_rad = math.radians(self.rotation)
        line_length = 50
        end_x = turret_x + line_length * math.cos(angle_rad)
        end_y = turret_y + line_length * math.sin(angle_rad)

        self.battlefield_canvas.create_line(turret_x, turret_y, end_x, end_y,
                                          fill='#ffff00', width=3)

        # Draw targets
        for target in self.targets:
            if target['destroyed']:
                # Destroyed target
                self.battlefield_canvas.create_oval(target['x']-15, target['y']-15,
                                                  target['x']+15, target['y']+15,
                                                  fill='#333333', outline='#666666')
                self.battlefield_canvas.create_text(target['x'], target['y'],
                                                  text="💀", font=('Arial', 16))
            else:
                # Active target
                if target['type'] == 'tank':
                    color = '#ff0000'
                    symbol = "🚗"
                elif target['type'] == 'vehicle':
                    color = '#ff6600'
                    symbol = "🚙"
                elif target['type'] == 'building':
                    color = '#666666'
                    symbol = "🏢"
                else:
                    color = '#ffff00'
                    symbol = "👤"

                self.battlefield_canvas.create_rectangle(target['x']-12, target['y']-12,
                                                       target['x']+12, target['y']+12,
                                                       fill=color, outline='white', width=2)

                # Health bars
                for i in range(target['health']):
                    self.battlefield_canvas.create_rectangle(target['x']-10+i*7, target['y']-18,
                                                           target['x']-3+i*7, target['y']-15,
                                                           fill='#00ff00', outline='')

                # Target lock indicator
                if target == self.active_target:
                    self.battlefield_canvas.create_oval(target['x']-20, target['y']-20,
                                                      target['x']+20, target['y']+20,
                                                      outline='#ff0000', width=3)
                    self.battlefield_canvas.create_text(target['x'], target['y']+25,
                                                      text="🎯 LOCKED", fill='#ff0000',
                                                      font=('Courier New', 8, 'bold'))

        # Muzzle flash effect
        if hasattr(self, 'muzzle_flash_time') and time.time() - self.muzzle_flash_time < 0.2:
            flash_x = turret_x + 30 * math.cos(angle_rad)
            flash_y = turret_y + 30 * math.sin(angle_rad)

            self.battlefield_canvas.create_oval(flash_x-15, flash_y-15, flash_x+15, flash_y+15,
                                              fill='#ffff00', outline='#ff6600', width=3)

    def draw_turret_display(self):
        """Draw turret circular display exactly like your image"""
        self.turret_canvas.delete("all")

        # Center coordinates
        cx, cy = 100, 65

        # Draw main circle (like your green circle)
        self.turret_canvas.create_oval(cx-50, cy-50, cx+50, cy+50,
                                     outline='#00ff00', width=3, fill='#001100')

        # Draw inner circle
        self.turret_canvas.create_oval(cx-30, cy-30, cx+30, cy+30,
                                     outline='#00ff00', width=2)

        # Draw turret direction line (white line like in your image)
        angle_rad = math.radians(self.rotation)
        line_length = 45
        end_x = cx + line_length * math.cos(angle_rad)
        end_y = cy + line_length * math.sin(angle_rad)

        self.turret_canvas.create_line(cx, cy, end_x, end_y,
                                     fill='white', width=4)

        # Draw center dot
        self.turret_canvas.create_oval(cx-3, cy-3, cx+3, cy+3,
                                     fill='#00ff00', outline='white')

        # Draw angle markings
        for angle in range(0, 360, 30):
            mark_rad = math.radians(angle)
            inner_x = cx + 35 * math.cos(mark_rad)
            inner_y = cy + 35 * math.sin(mark_rad)
            outer_x = cx + 45 * math.cos(mark_rad)
            outer_y = cy + 45 * math.sin(mark_rad)

            self.turret_canvas.create_line(inner_x, inner_y, outer_x, outer_y,
                                         fill='#00ff00', width=1)

    def draw_main_camera(self):
        """Draw main camera feed"""
        self.main_camera_canvas.delete("all")

        # Background
        self.main_camera_canvas.create_rectangle(0, 0, 360, 200, fill='#001100')

        # Grid overlay
        for i in range(0, 360, 30):
            self.main_camera_canvas.create_line(i, 0, i, 200, fill='#003300', width=1)
        for i in range(0, 200, 30):
            self.main_camera_canvas.create_line(0, i, 360, i, fill='#003300', width=1)

        # Crosshairs
        self.main_camera_canvas.create_line(180, 0, 180, 200, fill='#00ff00', width=2)
        self.main_camera_canvas.create_line(0, 100, 360, 100, fill='#00ff00', width=2)

        # Center reticle
        self.main_camera_canvas.create_oval(175, 95, 185, 105, outline='#00ff00', width=2)

        # Range circles
        for radius in [40, 80]:
            self.main_camera_canvas.create_oval(180-radius, 100-radius, 180+radius, 100+radius,
                                              outline='#003300', width=1)

        # Simulate targets in view
        for target in self.targets:
            if not target['destroyed']:
                # Convert battlefield to camera coordinates
                cam_x = (target['x'] / 800) * 360
                cam_y = (target['y'] / 500) * 200

                if 0 <= cam_x <= 360 and 0 <= cam_y <= 200:
                    # Target box
                    self.main_camera_canvas.create_rectangle(cam_x-8, cam_y-8, cam_x+8, cam_y+8,
                                                           outline='#ff0000', width=2)

                    # Target info
                    self.main_camera_canvas.create_text(cam_x, cam_y-15, text="HOSTILE",
                                                      fill='#ff0000', font=('Courier New', 6))

        # Camera overlay info
        self.main_camera_canvas.create_text(5, 5, text="📹 MAIN CAM",
                                          fill='#00ff00', anchor='nw', font=('Courier New', 8))

        self.main_camera_canvas.create_text(355, 5, text="1920x1080",
                                          fill='#ffff00', anchor='ne', font=('Courier New', 8))

        self.main_camera_canvas.create_text(5, 195, text=f"ROT: {self.rotation:.1f}°",
                                          fill='#00ff00', anchor='sw', font=('Courier New', 8))

        self.main_camera_canvas.create_text(355, 195, text=f"ELEV: {self.elevation:.1f}°",
                                          fill='#00ff00', anchor='se', font=('Courier New', 8))

    def draw_thermal_camera(self):
        """Draw thermal camera feed"""
        self.thermal_camera_canvas.delete("all")

        # Background (thermal colors)
        self.thermal_camera_canvas.create_rectangle(0, 0, 360, 200, fill='#330000')

        # Thermal grid
        for i in range(0, 360, 40):
            self.thermal_camera_canvas.create_line(i, 0, i, 200, fill='#660000', width=1)
        for i in range(0, 200, 40):
            self.thermal_camera_canvas.create_line(0, i, 360, i, fill='#660000', width=1)

        # Thermal crosshairs
        self.thermal_camera_canvas.create_line(180, 0, 180, 200, fill='#ff6600', width=2)
        self.thermal_camera_canvas.create_line(0, 100, 360, 100, fill='#ff6600', width=2)

        # Simulate thermal signatures
        for target in self.targets:
            if not target['destroyed']:
                cam_x = (target['x'] / 800) * 360
                cam_y = (target['y'] / 500) * 200

                if 0 <= cam_x <= 360 and 0 <= cam_y <= 200:
                    # Thermal signature based on target type
                    if target['thermal_signature'] > 70:
                        color = '#ff0000'  # Hot
                    elif target['thermal_signature'] > 40:
                        color = '#ff6600'  # Warm
                    else:
                        color = '#ffff00'  # Cool

                    # Draw thermal blob
                    size = target['thermal_signature'] / 10
                    self.thermal_camera_canvas.create_oval(cam_x-size, cam_y-size,
                                                         cam_x+size, cam_y+size,
                                                         fill=color, outline='')

                    # Temperature reading
                    temp = 20 + target['thermal_signature']
                    self.thermal_camera_canvas.create_text(cam_x, cam_y+15, text=f"{temp}°C",
                                                         fill='white', font=('Courier New', 6))

        # Thermal camera info
        self.thermal_camera_canvas.create_text(5, 5, text="🌡️ THERMAL",
                                             fill='#ff6600', anchor='nw', font=('Courier New', 8))

        self.thermal_camera_canvas.create_text(355, 5, text="640x480",
                                             fill='#ffff00', anchor='ne', font=('Courier New', 8))

        self.thermal_camera_canvas.create_text(180, 190, text="INFRARED IMAGING",
                                             fill='#ff6600', font=('Courier New', 8))

    def run(self):
        """Start the exact military turret simulation"""
        print("🎯 Starting Military Turret System - Exact Replica")
        print("📋 System Components:")
        print("   ✅ Fixed Base")
        print("   ✅ Turret Servo Motor")
        print("   ✅ Electronic Speed Controller (ESC)")
        print("   ✅ Main Battery Pack")
        print("   ✅ Main Camera")
        print("   ✅ Thermal/IR Camera")
        print("   ✅ Gun Mount/Module")
        print("\n🎮 Controls:")
        print("   - Use direction buttons to aim turret")
        print("   - Red fire button to engage targets")
        print("   - Enable auto tracking for automatic targeting")
        print("   - Monitor all system components in real-time")
        print("\n🎯 Mission: Engage and destroy all hostile targets!")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("System shutdown")

if __name__ == '__main__':
    print("🚀 Initializing Military Turret Control System...")

    # Check dependencies
    try:
        import pygame
        print("✅ Audio system ready")
    except ImportError:
        print("⚠️ Audio system unavailable")

    try:
        from PIL import Image, ImageTk
        print("✅ Graphics system ready")
    except ImportError:
        print("⚠️ Basic graphics mode")

    print("🎯 Launching exact replica simulation...")

    # Start the exact simulation
    sim = MilitaryTurretExact()
    sim.run()
