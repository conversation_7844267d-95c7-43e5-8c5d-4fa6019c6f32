<?xml version="1.0"?>
<package format="2">
  <name>turret_simulation</name>
  <version>1.0.0</version>
  <description>Complete military turret simulation for ROS/Gazebo</description>

  <maintainer email="<EMAIL>">Turret Simulation Team</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- Core ROS dependencies -->
  <build_depend>rospy</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>

  <!-- Gazebo dependencies -->
  <build_depend>gazebo_ros</build_depend>
  <build_depend>gazebo_plugins</build_depend>
  <build_depend>gazebo_msgs</build_depend>

  <!-- Control dependencies -->
  <build_depend>controller_manager</build_depend>
  <build_depend>joint_state_controller</build_depend>
  <build_depend>position_controllers</build_depend>
  <build_depend>effort_controllers</build_depend>

  <!-- URDF and robot description -->
  <build_depend>urdf</build_depend>
  <build_depend>xacro</build_depend>
  <build_depend>robot_state_publisher</build_depend>

  <!-- Visualization -->
  <build_depend>rviz</build_depend>

  <!-- Runtime dependencies -->
  <exec_depend>rospy</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  <exec_depend>gazebo_plugins</exec_depend>
  <exec_depend>gazebo_msgs</exec_depend>
  <exec_depend>controller_manager</exec_depend>
  <exec_depend>joint_state_controller</exec_depend>
  <exec_depend>position_controllers</exec_depend>
  <exec_depend>effort_controllers</exec_depend>
  <exec_depend>urdf</exec_depend>
  <exec_depend>xacro</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>rviz</exec_depend>

  <export>
    <gazebo_ros gazebo_model_path="${prefix}/models"/>
    <gazebo_ros gazebo_media_path="${prefix}/media"/>
  </export>
</package>
