#!/usr/bin/env python3
"""
Realistic Military Turret Simulation with Firing System
شبیه‌سازی واقعی سیستم توپ نظامی با قابلیت شلیک
"""

import tkinter as tk
from tkinter import ttk, messagebox
import math
import time
import threading
import random
from PIL import Image, ImageTk
import pygame
import numpy as np

class RealisticTurretSim:
    def __init__(self):
        # Initialize pygame for sound effects
        pygame.mixer.init()
        
        self.root = tk.Tk()
        self.root.title("🎯 Military Turret Simulation - Realistic Firing System")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1a1a1a')
        
        # Turret state
        self.rotation = 0.0
        self.elevation = 0.0
        self.target_rotation = 0.0
        self.target_elevation = 0.0
        
        # Firing system
        self.ammo_count = 100
        self.max_ammo = 100
        self.firing_rate = 600  # rounds per minute
        self.last_shot_time = 0
        self.auto_fire = False
        self.firing_mode = "single"  # single, burst, auto
        self.burst_count = 0
        self.max_burst = 3
        
        # Projectiles and effects
        self.projectiles = []
        self.explosions = []
        self.muzzle_flash = False
        self.muzzle_flash_time = 0
        
        # Targets
        self.targets = []
        self.target_hits = 0
        self.accuracy = 0.85  # 85% accuracy
        
        # System status
        self.system_temperature = 20  # Celsius
        self.barrel_wear = 0  # 0-100%
        self.system_ready = True
        self.overheated = False
        
        # Control parameters
        self.rotation_min = -180
        self.rotation_max = 180
        self.elevation_min = -10
        self.elevation_max = 85
        self.movement_speed = 2.0
        
        # Patrol and tracking
        self.control_mode = "manual"
        self.patrol_active = False
        self.tracking_target = None
        
        # Sound effects (simulated)
        self.sound_enabled = True
        
        self.create_realistic_gui()
        self.generate_targets()
        self.start_simulation_thread()
        
    def create_realistic_gui(self):
        """Create realistic military-style GUI"""
        # Main container with military styling
        main_frame = tk.Frame(self.root, bg='#1a1a1a', relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Title with military styling
        title_frame = tk.Frame(main_frame, bg='#2d4a2d', relief='raised', bd=2)
        title_frame.pack(fill='x', pady=(0, 5))
        
        title_label = tk.Label(title_frame, text="🎯 MILITARY TURRET CONTROL SYSTEM - FIRING MODE", 
                              font=('Courier New', 14, 'bold'), bg='#2d4a2d', fg='#00ff00')
        title_label.pack(pady=5)
        
        # Status bar
        status_frame = tk.Frame(main_frame, bg='#333333', relief='sunken', bd=2)
        status_frame.pack(fill='x', pady=(0, 5))
        
        self.status_text = tk.StringVar(value="SYSTEM READY - WEAPONS HOT")
        status_label = tk.Label(status_frame, textvariable=self.status_text, 
                               font=('Courier New', 10), bg='#333333', fg='#00ff00')
        status_label.pack(pady=2)
        
        # Main control area
        control_frame = tk.Frame(main_frame, bg='#1a1a1a')
        control_frame.pack(fill='both', expand=True)
        
        # Left panel - Weapon systems
        weapon_frame = tk.LabelFrame(control_frame, text="🔫 WEAPON SYSTEMS", 
                                   font=('Courier New', 10, 'bold'), 
                                   bg='#2a2a2a', fg='#ff6600', relief='raised', bd=2)
        weapon_frame.pack(side='left', fill='y', padx=(0, 5), pady=5)
        
        # Ammo display
        ammo_frame = tk.Frame(weapon_frame, bg='#2a2a2a')
        ammo_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(ammo_frame, text="AMMUNITION:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack()
        
        self.ammo_var = tk.StringVar(value=f"{self.ammo_count}/{self.max_ammo}")
        ammo_label = tk.Label(ammo_frame, textvariable=self.ammo_var, 
                             font=('Courier New', 12, 'bold'), bg='#2a2a2a', fg='#00ff00')
        ammo_label.pack()
        
        # Ammo progress bar
        self.ammo_progress = ttk.Progressbar(ammo_frame, length=150, mode='determinate')
        self.ammo_progress.pack(pady=2)
        self.ammo_progress['value'] = 100
        
        # Firing controls
        fire_frame = tk.Frame(weapon_frame, bg='#2a2a2a')
        fire_frame.pack(fill='x', padx=5, pady=10)
        
        # Fire button (large and prominent)
        self.fire_button = tk.Button(fire_frame, text="🔥 FIRE 🔥", 
                                    font=('Courier New', 14, 'bold'),
                                    bg='#ff0000', fg='white', relief='raised', bd=3,
                                    command=self.fire_weapon, width=12, height=2)
        self.fire_button.pack(pady=5)
        
        # Firing mode selection
        mode_frame = tk.Frame(weapon_frame, bg='#2a2a2a')
        mode_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(mode_frame, text="FIRING MODE:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack()
        
        self.firing_mode_var = tk.StringVar(value="single")
        modes = [("Single", "single"), ("Burst (3)", "burst"), ("Auto", "auto")]
        
        for text, value in modes:
            tk.Radiobutton(mode_frame, text=text, variable=self.firing_mode_var, value=value,
                          font=('Courier New', 8), bg='#2a2a2a', fg='white', 
                          selectcolor='#ff6600', command=self.change_firing_mode).pack(anchor='w')
        
        # Auto fire toggle
        self.auto_fire_var = tk.BooleanVar()
        auto_fire_check = tk.Checkbutton(mode_frame, text="AUTO FIRE", 
                                        variable=self.auto_fire_var,
                                        font=('Courier New', 9, 'bold'), 
                                        bg='#2a2a2a', fg='#ff0000', selectcolor='#ff6600',
                                        command=self.toggle_auto_fire)
        auto_fire_check.pack(pady=5)
        
        # System status
        sys_frame = tk.Frame(weapon_frame, bg='#2a2a2a')
        sys_frame.pack(fill='x', padx=5, pady=10)
        
        tk.Label(sys_frame, text="SYSTEM STATUS:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack()
        
        # Temperature gauge
        temp_frame = tk.Frame(sys_frame, bg='#2a2a2a')
        temp_frame.pack(fill='x', pady=2)
        
        tk.Label(temp_frame, text="TEMP:", font=('Courier New', 8), 
                bg='#2a2a2a', fg='white').pack(side='left')
        
        self.temp_var = tk.StringVar(value="20°C")
        temp_label = tk.Label(temp_frame, textvariable=self.temp_var, 
                             font=('Courier New', 8, 'bold'), bg='#2a2a2a', fg='#00ff00')
        temp_label.pack(side='right')
        
        # Barrel wear
        wear_frame = tk.Frame(sys_frame, bg='#2a2a2a')
        wear_frame.pack(fill='x', pady=2)
        
        tk.Label(wear_frame, text="BARREL:", font=('Courier New', 8), 
                bg='#2a2a2a', fg='white').pack(side='left')
        
        self.wear_var = tk.StringVar(value="0%")
        wear_label = tk.Label(wear_frame, textvariable=self.wear_var, 
                             font=('Courier New', 8, 'bold'), bg='#2a2a2a', fg='#00ff00')
        wear_label.pack(side='right')
        
        # Reload button
        reload_button = tk.Button(weapon_frame, text="🔄 RELOAD", 
                                 font=('Courier New', 10, 'bold'),
                                 bg='#0066cc', fg='white', relief='raised', bd=2,
                                 command=self.reload_ammo, width=15)
        reload_button.pack(pady=10)
        
        # Center panel - Battlefield view
        battlefield_frame = tk.LabelFrame(control_frame, text="🎯 BATTLEFIELD VIEW", 
                                        font=('Courier New', 10, 'bold'), 
                                        bg='#2a2a2a', fg='#00ff00', relief='raised', bd=2)
        battlefield_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        
        # Main battlefield canvas
        self.battlefield_canvas = tk.Canvas(battlefield_frame, width=600, height=400, 
                                          bg='#001100', relief='sunken', bd=2)
        self.battlefield_canvas.pack(pady=10)
        
        # Turret control canvas
        turret_frame = tk.Frame(battlefield_frame, bg='#2a2a2a')
        turret_frame.pack(fill='x', pady=5)
        
        self.turret_canvas = tk.Canvas(turret_frame, width=300, height=200, 
                                     bg='#000000', relief='sunken', bd=2)
        self.turret_canvas.pack(side='left', padx=5)
        
        # Camera feed
        camera_frame = tk.Frame(turret_frame, bg='#2a2a2a')
        camera_frame.pack(side='right', padx=5)
        
        tk.Label(camera_frame, text="📹 CAMERA FEED", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#00ff00').pack()
        
        self.camera_canvas = tk.Canvas(camera_frame, width=280, height=160, 
                                     bg='#003300', relief='sunken', bd=2)
        self.camera_canvas.pack()
        
        # Right panel - Position and targeting
        target_frame = tk.LabelFrame(control_frame, text="🎯 TARGETING SYSTEM", 
                                   font=('Courier New', 10, 'bold'), 
                                   bg='#2a2a2a', fg='#ff6600', relief='raised', bd=2)
        target_frame.pack(side='right', fill='y', padx=(5, 0), pady=5)
        
        # Position display
        pos_frame = tk.Frame(target_frame, bg='#2a2a2a')
        pos_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(pos_frame, text="POSITION:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack()
        
        # Rotation
        rot_frame = tk.Frame(pos_frame, bg='#2a2a2a')
        rot_frame.pack(fill='x', pady=2)
        
        tk.Label(rot_frame, text="ROT:", font=('Courier New', 8), 
                bg='#2a2a2a', fg='white').pack(side='left')
        
        self.rotation_var = tk.StringVar(value="0.0°")
        rot_label = tk.Label(rot_frame, textvariable=self.rotation_var, 
                           font=('Courier New', 10, 'bold'), bg='#2a2a2a', fg='#00ff00')
        rot_label.pack(side='right')
        
        # Elevation
        elev_frame = tk.Frame(pos_frame, bg='#2a2a2a')
        elev_frame.pack(fill='x', pady=2)
        
        tk.Label(elev_frame, text="ELEV:", font=('Courier New', 8), 
                bg='#2a2a2a', fg='white').pack(side='left')
        
        self.elevation_var = tk.StringVar(value="0.0°")
        elev_label = tk.Label(elev_frame, textvariable=self.elevation_var, 
                            font=('Courier New', 10, 'bold'), bg='#2a2a2a', fg='#00ff00')
        elev_label.pack(side='right')
        
        # Manual controls
        manual_frame = tk.Frame(target_frame, bg='#2a2a2a')
        manual_frame.pack(fill='x', padx=5, pady=10)
        
        tk.Label(manual_frame, text="MANUAL CONTROL:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack()
        
        # Direction buttons with military styling
        btn_frame = tk.Frame(manual_frame, bg='#2a2a2a')
        btn_frame.pack(pady=5)
        
        tk.Button(btn_frame, text="▲", font=('Courier New', 12, 'bold'), 
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(0, 5)).grid(row=0, column=1, padx=1, pady=1)
        
        tk.Button(btn_frame, text="◄", font=('Courier New', 12, 'bold'), 
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(-10, 0)).grid(row=1, column=0, padx=1, pady=1)
        
        tk.Button(btn_frame, text="STOP", font=('Courier New', 8, 'bold'), 
                 bg='#ff0000', fg='white', width=4, height=1,
                 command=self.stop_turret).grid(row=1, column=1, padx=1, pady=1)
        
        tk.Button(btn_frame, text="►", font=('Courier New', 12, 'bold'), 
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(10, 0)).grid(row=1, column=2, padx=1, pady=1)
        
        tk.Button(btn_frame, text="▼", font=('Courier New', 12, 'bold'), 
                 bg='#4a4a4a', fg='white', width=3, height=1,
                 command=lambda: self.manual_move(0, -5)).grid(row=2, column=1, padx=1, pady=1)
        
        # Target statistics
        stats_frame = tk.Frame(target_frame, bg='#2a2a2a')
        stats_frame.pack(fill='x', padx=5, pady=10)
        
        tk.Label(stats_frame, text="COMBAT STATS:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ffff00').pack()
        
        # Hits
        hits_frame = tk.Frame(stats_frame, bg='#2a2a2a')
        hits_frame.pack(fill='x', pady=2)
        
        tk.Label(hits_frame, text="HITS:", font=('Courier New', 8), 
                bg='#2a2a2a', fg='white').pack(side='left')
        
        self.hits_var = tk.StringVar(value="0")
        hits_label = tk.Label(hits_frame, textvariable=self.hits_var, 
                            font=('Courier New', 10, 'bold'), bg='#2a2a2a', fg='#ff0000')
        hits_label.pack(side='right')
        
        # Targets remaining
        targets_frame = tk.Frame(stats_frame, bg='#2a2a2a')
        targets_frame.pack(fill='x', pady=2)
        
        tk.Label(targets_frame, text="TARGETS:", font=('Courier New', 8), 
                bg='#2a2a2a', fg='white').pack(side='left')
        
        self.targets_var = tk.StringVar(value="0")
        targets_label = tk.Label(targets_frame, textvariable=self.targets_var, 
                               font=('Courier New', 10, 'bold'), bg='#2a2a2a', fg='#ff6600')
        targets_label.pack(side='right')
        
        # Emergency controls
        emergency_frame = tk.Frame(target_frame, bg='#2a2a2a')
        emergency_frame.pack(fill='x', padx=5, pady=20)
        
        tk.Label(emergency_frame, text="EMERGENCY:", font=('Courier New', 9, 'bold'), 
                bg='#2a2a2a', fg='#ff0000').pack()
        
        tk.Button(emergency_frame, text="🚨 CEASE FIRE 🚨", 
                 font=('Courier New', 10, 'bold'),
                 bg='#cc0000', fg='white', relief='raised', bd=3,
                 command=self.cease_fire, width=15).pack(pady=2)
        
        tk.Button(emergency_frame, text="🏠 HOME", 
                 font=('Courier New', 9, 'bold'),
                 bg='#0066cc', fg='white', relief='raised', bd=2,
                 command=self.home_position, width=15).pack(pady=2)
        
        # Bind keyboard controls
        self.root.bind('<KeyPress>', self.on_key_press)
        self.root.focus_set()
        
    def generate_targets(self):
        """Generate random targets on battlefield"""
        self.targets = []
        for i in range(random.randint(3, 8)):
            target = {
                'x': random.randint(50, 550),
                'y': random.randint(50, 350),
                'type': random.choice(['tank', 'vehicle', 'building']),
                'health': random.randint(1, 3),
                'destroyed': False
            }
            self.targets.append(target)
            
    def start_simulation_thread(self):
        """Start the main simulation loop"""
        self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
        self.simulation_thread.start()
        
    def simulation_loop(self):
        """Main simulation loop with realistic physics"""
        while True:
            try:
                current_time = time.time()
                
                # Update turret position
                self.update_turret_position()
                
                # Update projectiles
                self.update_projectiles()
                
                # Update explosions
                self.update_explosions()
                
                # Update system temperature
                self.update_system_status()
                
                # Handle auto fire
                if self.auto_fire and self.can_fire():
                    self.fire_weapon()
                
                # Update muzzle flash
                if self.muzzle_flash and current_time - self.muzzle_flash_time > 0.1:
                    self.muzzle_flash = False
                
                # Update GUI
                self.root.after(0, self.update_gui)
                
                time.sleep(0.02)  # 50 Hz update rate
                
            except Exception as e:
                print(f"Simulation error: {e}")
                time.sleep(0.1)

    def update_turret_position(self):
        """Update turret position with realistic movement"""
        # Rotation
        rot_diff = self.target_rotation - self.rotation
        if abs(rot_diff) > 0.1:
            move_amount = min(abs(rot_diff), self.movement_speed)
            if rot_diff > 0:
                self.rotation += move_amount
            else:
                self.rotation -= move_amount

        # Elevation
        elev_diff = self.target_elevation - self.elevation
        if abs(elev_diff) > 0.1:
            move_amount = min(abs(elev_diff), self.movement_speed)
            if elev_diff > 0:
                self.elevation += move_amount
            else:
                self.elevation -= move_amount

        # Apply limits
        self.rotation = max(self.rotation_min, min(self.rotation_max, self.rotation))
        self.elevation = max(self.elevation_min, min(self.elevation_max, self.elevation))

    def fire_weapon(self):
        """Fire the weapon with realistic ballistics"""
        if not self.can_fire():
            return

        current_time = time.time()

        # Check firing rate
        time_since_last_shot = current_time - self.last_shot_time
        min_interval = 60.0 / self.firing_rate  # Convert RPM to seconds

        if time_since_last_shot < min_interval:
            return

        # Handle different firing modes
        if self.firing_mode == "burst":
            if self.burst_count >= self.max_burst:
                return
            self.burst_count += 1
            # Reset burst after delay
            self.root.after(500, lambda: setattr(self, 'burst_count', 0))

        # Create projectile
        projectile = {
            'x': 300,  # Turret center
            'y': 200,
            'angle': math.radians(self.rotation + self.elevation),
            'speed': 800,  # pixels per second
            'life': 2.0,  # seconds
            'start_time': current_time
        }

        # Add some inaccuracy
        accuracy_factor = random.uniform(0.95, 1.05) if random.random() < self.accuracy else random.uniform(0.8, 1.2)
        projectile['angle'] *= accuracy_factor

        self.projectiles.append(projectile)

        # Update ammo
        self.ammo_count = max(0, self.ammo_count - 1)

        # Update system status
        self.system_temperature += random.uniform(2, 5)
        self.barrel_wear += 0.1

        # Muzzle flash effect
        self.muzzle_flash = True
        self.muzzle_flash_time = current_time

        # Sound effect (simulated)
        if self.sound_enabled:
            print("💥 BANG!")

        self.last_shot_time = current_time

    def update_projectiles(self):
        """Update projectile positions and check for hits"""
        current_time = time.time()

        for projectile in self.projectiles[:]:
            # Calculate position
            elapsed = current_time - projectile['start_time']

            if elapsed > projectile['life']:
                self.projectiles.remove(projectile)
                continue

            # Update position
            projectile['x'] += projectile['speed'] * math.cos(projectile['angle']) * 0.02
            projectile['y'] += projectile['speed'] * math.sin(projectile['angle']) * 0.02

            # Check for target hits
            for target in self.targets:
                if target['destroyed']:
                    continue

                distance = math.sqrt((projectile['x'] - target['x'])**2 + (projectile['y'] - target['y'])**2)

                if distance < 20:  # Hit radius
                    # Create explosion
                    explosion = {
                        'x': target['x'],
                        'y': target['y'],
                        'size': 0,
                        'max_size': 40,
                        'start_time': current_time
                    }
                    self.explosions.append(explosion)

                    # Damage target
                    target['health'] -= 1
                    if target['health'] <= 0:
                        target['destroyed'] = True
                        self.target_hits += 1

                    # Remove projectile
                    if projectile in self.projectiles:
                        self.projectiles.remove(projectile)
                    break

            # Remove projectiles that go off screen
            if (projectile['x'] < 0 or projectile['x'] > 600 or
                projectile['y'] < 0 or projectile['y'] > 400):
                if projectile in self.projectiles:
                    self.projectiles.remove(projectile)

    def update_explosions(self):
        """Update explosion animations"""
        current_time = time.time()

        for explosion in self.explosions[:]:
            elapsed = current_time - explosion['start_time']

            if elapsed > 0.5:  # Explosion duration
                self.explosions.remove(explosion)
                continue

            # Animate explosion size
            progress = elapsed / 0.5
            explosion['size'] = explosion['max_size'] * (1 - progress)

    def update_system_status(self):
        """Update system temperature and status"""
        # Cool down system
        if self.system_temperature > 20:
            self.system_temperature -= 0.5

        # Check for overheating
        if self.system_temperature > 80:
            self.overheated = True
            self.system_ready = False
        elif self.system_temperature < 60:
            self.overheated = False
            self.system_ready = True

    def can_fire(self):
        """Check if weapon can fire"""
        return (self.ammo_count > 0 and
                self.system_ready and
                not self.overheated)

    def change_firing_mode(self):
        """Change firing mode"""
        self.firing_mode = self.firing_mode_var.get()
        self.burst_count = 0

    def toggle_auto_fire(self):
        """Toggle auto fire mode"""
        self.auto_fire = self.auto_fire_var.get()

    def reload_ammo(self):
        """Reload ammunition"""
        self.ammo_count = self.max_ammo

    def manual_move(self, rotation_delta, elevation_delta):
        """Manual turret movement"""
        self.target_rotation = max(self.rotation_min,
                                 min(self.rotation_max,
                                     self.rotation + rotation_delta))
        self.target_elevation = max(self.elevation_min,
                                  min(self.elevation_max,
                                      self.elevation + elevation_delta))

    def stop_turret(self):
        """Stop turret movement"""
        self.target_rotation = self.rotation
        self.target_elevation = self.elevation

    def cease_fire(self):
        """Emergency cease fire"""
        self.auto_fire = False
        self.auto_fire_var.set(False)
        self.projectiles.clear()
        messagebox.showwarning("CEASE FIRE", "🚨 ALL WEAPONS SYSTEMS DISABLED 🚨")

    def home_position(self):
        """Return to home position"""
        self.target_rotation = 0.0
        self.target_elevation = 0.0

    def on_key_press(self, event):
        """Handle keyboard controls"""
        key = event.keysym.lower()

        if key == 'space':
            self.fire_weapon()
        elif key == 'r':
            self.reload_ammo()
        elif key == 'h':
            self.home_position()
        elif key == 'c':
            self.cease_fire()
        elif key == 'up':
            self.manual_move(0, 5)
        elif key == 'down':
            self.manual_move(0, -5)
        elif key == 'left':
            self.manual_move(-10, 0)
        elif key == 'right':
            self.manual_move(10, 0)

    def update_gui(self):
        """Update all GUI elements"""
        # Update position displays
        self.rotation_var.set(f"{self.rotation:.1f}°")
        self.elevation_var.set(f"{self.elevation:.1f}°")

        # Update ammo
        self.ammo_var.set(f"{self.ammo_count}/{self.max_ammo}")
        self.ammo_progress['value'] = (self.ammo_count / self.max_ammo) * 100

        # Update system status
        temp_color = '#00ff00'
        if self.system_temperature > 60:
            temp_color = '#ffff00'
        if self.system_temperature > 80:
            temp_color = '#ff0000'

        self.temp_var.set(f"{self.system_temperature:.0f}°C")

        wear_color = '#00ff00'
        if self.barrel_wear > 50:
            wear_color = '#ffff00'
        if self.barrel_wear > 80:
            wear_color = '#ff0000'

        self.wear_var.set(f"{self.barrel_wear:.0f}%")

        # Update combat stats
        self.hits_var.set(str(self.target_hits))
        active_targets = len([t for t in self.targets if not t['destroyed']])
        self.targets_var.set(str(active_targets))

        # Update status text
        if self.overheated:
            status = "🚨 SYSTEM OVERHEATED - COOLING DOWN 🚨"
        elif self.ammo_count == 0:
            status = "⚠️ OUT OF AMMUNITION - RELOAD REQUIRED ⚠️"
        elif self.auto_fire:
            status = "🔥 AUTO FIRE ENGAGED - WEAPONS HOT 🔥"
        else:
            status = "✅ SYSTEM READY - WEAPONS HOT ✅"

        self.status_text.set(status)

        # Update fire button state
        if self.can_fire():
            self.fire_button.config(state='normal', bg='#ff0000')
        else:
            self.fire_button.config(state='disabled', bg='#666666')

        # Draw battlefield
        self.draw_battlefield()
        self.draw_turret()
        self.draw_camera_feed()

    def draw_battlefield(self):
        """Draw the battlefield with targets and effects"""
        self.battlefield_canvas.delete("all")

        # Background grid
        for i in range(0, 600, 50):
            self.battlefield_canvas.create_line(i, 0, i, 400, fill='#003300', width=1)
        for i in range(0, 400, 50):
            self.battlefield_canvas.create_line(0, i, 600, i, fill='#003300', width=1)

        # Draw targets
        for target in self.targets:
            if target['destroyed']:
                # Draw destroyed target
                self.battlefield_canvas.create_oval(target['x']-15, target['y']-15,
                                                  target['x']+15, target['y']+15,
                                                  fill='#333333', outline='#666666')
                self.battlefield_canvas.create_text(target['x'], target['y'],
                                                  text="💀", font=('Arial', 12))
            else:
                # Draw active target
                color = '#ff0000' if target['type'] == 'tank' else '#ff6600'
                self.battlefield_canvas.create_rectangle(target['x']-10, target['y']-10,
                                                       target['x']+10, target['y']+10,
                                                       fill=color, outline='white', width=2)

                # Health indicator
                for i in range(target['health']):
                    self.battlefield_canvas.create_rectangle(target['x']-8+i*5, target['y']-15,
                                                           target['x']-3+i*5, target['y']-12,
                                                           fill='#00ff00', outline='')

        # Draw projectiles
        for projectile in self.projectiles:
            self.battlefield_canvas.create_oval(projectile['x']-2, projectile['y']-2,
                                              projectile['x']+2, projectile['y']+2,
                                              fill='#ffff00', outline='#ffffff')

            # Draw tracer trail
            trail_length = 20
            trail_x = projectile['x'] - trail_length * math.cos(projectile['angle'])
            trail_y = projectile['y'] - trail_length * math.sin(projectile['angle'])

            self.battlefield_canvas.create_line(trail_x, trail_y, projectile['x'], projectile['y'],
                                              fill='#ffaa00', width=2)

        # Draw explosions
        for explosion in self.explosions:
            size = explosion['size']
            colors = ['#ffff00', '#ff6600', '#ff0000']

            for i, color in enumerate(colors):
                radius = size * (1 - i * 0.3)
                if radius > 0:
                    self.battlefield_canvas.create_oval(explosion['x']-radius, explosion['y']-radius,
                                                      explosion['x']+radius, explosion['y']+radius,
                                                      fill=color, outline='')

        # Draw turret position indicator
        turret_x, turret_y = 300, 200
        self.battlefield_canvas.create_oval(turret_x-5, turret_y-5, turret_x+5, turret_y+5,
                                          fill='#00ff00', outline='white', width=2)

        # Draw firing line
        if self.muzzle_flash:
            line_length = 100
            line_end_x = turret_x + line_length * math.cos(math.radians(self.rotation))
            line_end_y = turret_y + line_length * math.sin(math.radians(self.rotation))

            self.battlefield_canvas.create_line(turret_x, turret_y, line_end_x, line_end_y,
                                              fill='#ffff00', width=3)

    def draw_turret(self):
        """Draw detailed turret visualization"""
        self.turret_canvas.delete("all")

        # Canvas center
        cx, cy = 150, 100

        # Draw base
        self.turret_canvas.create_oval(cx-40, cy-40, cx+40, cy+40,
                                     fill='#2d4a2d', outline='#00ff00', width=2)

        # Draw turret body (rotated)
        rot_rad = math.radians(self.rotation)
        turret_x = cx + 25 * math.cos(rot_rad)
        turret_y = cy + 25 * math.sin(rot_rad)

        # Draw gun barrel (with elevation)
        elev_rad = math.radians(self.elevation)
        barrel_length = 50
        barrel_end_x = turret_x + barrel_length * math.cos(rot_rad)
        barrel_end_y = turret_y + barrel_length * math.sin(rot_rad) - barrel_length * 0.3 * math.sin(elev_rad)

        # Barrel
        self.turret_canvas.create_line(turret_x, turret_y, barrel_end_x, barrel_end_y,
                                     fill='#666666', width=6)

        # Muzzle flash
        if self.muzzle_flash:
            flash_size = 15
            self.turret_canvas.create_oval(barrel_end_x-flash_size, barrel_end_y-flash_size,
                                         barrel_end_x+flash_size, barrel_end_y+flash_size,
                                         fill='#ffff00', outline='#ff6600', width=2)

        # Turret body
        self.turret_canvas.create_oval(turret_x-12, turret_y-12, turret_x+12, turret_y+12,
                                     fill='#4a4a4a', outline='#00ff00', width=2)

        # Direction indicator
        self.turret_canvas.create_line(cx, cy, turret_x, turret_y, fill='#ffff00', width=2)

        # Angle displays
        self.turret_canvas.create_text(10, 10, text=f"ROT: {self.rotation:.1f}°",
                                     fill='#00ff00', anchor='nw', font=('Courier New', 8))
        self.turret_canvas.create_text(10, 25, text=f"ELEV: {self.elevation:.1f}°",
                                     fill='#00ff00', anchor='nw', font=('Courier New', 8))

        # System status indicators
        status_y = 180
        if self.overheated:
            self.turret_canvas.create_text(150, status_y, text="🚨 OVERHEATED",
                                         fill='#ff0000', font=('Courier New', 10, 'bold'))
        elif self.ammo_count == 0:
            self.turret_canvas.create_text(150, status_y, text="⚠️ NO AMMO",
                                         fill='#ffff00', font=('Courier New', 10, 'bold'))
        else:
            self.turret_canvas.create_text(150, status_y, text="✅ READY",
                                         fill='#00ff00', font=('Courier New', 10, 'bold'))

    def draw_camera_feed(self):
        """Draw realistic camera feed with targeting"""
        self.camera_canvas.delete("all")

        # Background
        self.camera_canvas.create_rectangle(0, 0, 280, 160, fill='#001100')

        # Grid overlay
        for i in range(0, 280, 20):
            self.camera_canvas.create_line(i, 0, i, 160, fill='#003300', width=1)
        for i in range(0, 160, 20):
            self.camera_canvas.create_line(0, i, 280, i, fill='#003300', width=1)

        # Crosshairs
        self.camera_canvas.create_line(140, 0, 140, 160, fill='#00ff00', width=2)
        self.camera_canvas.create_line(0, 80, 280, 80, fill='#00ff00', width=2)

        # Center reticle
        self.camera_canvas.create_oval(135, 75, 145, 85, outline='#00ff00', width=2)

        # Range circles
        for radius in [30, 60, 90]:
            self.camera_canvas.create_oval(140-radius, 80-radius, 140+radius, 80+radius,
                                         outline='#003300', width=1)

        # Simulate targets in camera view
        for target in self.targets:
            if not target['destroyed']:
                # Convert battlefield coordinates to camera view
                cam_x = (target['x'] / 600) * 280
                cam_y = (target['y'] / 400) * 160

                # Only show targets in camera field of view
                if 0 <= cam_x <= 280 and 0 <= cam_y <= 160:
                    # Target box
                    self.camera_canvas.create_rectangle(cam_x-8, cam_y-8, cam_x+8, cam_y+8,
                                                      outline='#ff0000', width=2)

                    # Target label
                    self.camera_canvas.create_text(cam_x, cam_y-15, text="HOSTILE",
                                                 fill='#ff0000', font=('Courier New', 6))

                    # Distance indicator
                    distance = math.sqrt((target['x']-300)**2 + (target['y']-200)**2)
                    self.camera_canvas.create_text(cam_x, cam_y+15, text=f"{distance:.0f}m",
                                                 fill='#ffff00', font=('Courier New', 6))

        # Camera info overlay
        self.camera_canvas.create_text(5, 5, text="📹 MAIN CAMERA",
                                     fill='#00ff00', anchor='nw', font=('Courier New', 8))

        # Zoom level
        self.camera_canvas.create_text(5, 150, text="ZOOM: 1.0x",
                                     fill='#00ff00', anchor='sw', font=('Courier New', 8))

        # Targeting mode
        mode_text = f"MODE: {self.firing_mode.upper()}"
        self.camera_canvas.create_text(275, 5, text=mode_text,
                                     fill='#ffff00', anchor='ne', font=('Courier New', 8))

        # Ammo counter
        self.camera_canvas.create_text(275, 150, text=f"AMMO: {self.ammo_count}",
                                     fill='#ff6600', anchor='se', font=('Courier New', 8))

        # Temperature warning
        if self.system_temperature > 70:
            self.camera_canvas.create_text(140, 20, text="⚠️ HIGH TEMP ⚠️",
                                         fill='#ff0000', font=('Courier New', 8, 'bold'))

        # Auto fire indicator
        if self.auto_fire:
            self.camera_canvas.create_text(140, 140, text="🔥 AUTO FIRE 🔥",
                                         fill='#ff0000', font=('Courier New', 8, 'bold'))

    def run(self):
        """Start the realistic simulation"""
        print("🎯 Starting Realistic Military Turret Simulation...")
        print("💥 Features: Realistic firing, ballistics, explosions, and combat!")
        print("\n🎮 Controls:")
        print("  - Click FIRE button or press SPACE to shoot")
        print("  - Use arrow keys or GUI buttons to aim")
        print("  - Press R to reload, H for home, C for cease fire")
        print("  - Try different firing modes: Single, Burst, Auto")
        print("  - Enable Auto Fire for continuous shooting")
        print("\n🎯 Objective: Destroy all targets on the battlefield!")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("Simulation stopped")

if __name__ == '__main__':
    # Check for required modules
    try:
        import pygame
        print("✅ Pygame found - Sound effects enabled")
    except ImportError:
        print("⚠️ Pygame not found - Running without sound effects")

    try:
        from PIL import Image, ImageTk
        print("✅ PIL found - Enhanced graphics enabled")
    except ImportError:
        print("⚠️ PIL not found - Running with basic graphics")

    # Start the simulation
    sim = RealisticTurretSim()
    sim.run()
