@echo off
echo ========================================
echo    Military Turret Simulation Launcher
echo ========================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not in PATH
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo Docker found. Checking if Docker is running...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)

echo.
echo Select simulation mode:
echo 1. Complete Simulation (Gazebo + UI + Cameras)
echo 2. Gazebo Only
echo 3. UI Only (requires Gazebo running separately)
echo 4. Build Docker Image Only
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo Starting complete turret simulation...
    docker-compose up turret-simulation
) else if "%choice%"=="2" (
    echo Starting Gazebo simulation only...
    docker-compose up turret-gazebo-only
) else if "%choice%"=="3" (
    echo Starting UI only...
    docker-compose up turret-ui-only
) else if "%choice%"=="4" (
    echo Building Docker image...
    docker-compose build
    echo Build complete!
) else (
    echo Invalid choice. Please run the script again.
)

echo.
echo Simulation ended. Press any key to exit.
pause >nul
