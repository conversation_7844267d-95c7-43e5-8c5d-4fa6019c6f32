#!/bin/bash

echo "========================================"
echo "   Military Turret Simulation Launcher"
echo "========================================"
echo

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker is not installed"
    echo "Please install Docker from: https://www.docker.com/get-docker"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "ERROR: Docker is not running"
    echo "Please start Docker and try again"
    exit 1
fi

echo "Docker found and running."
echo

echo "Select simulation mode:"
echo "1. Complete Simulation (Gazebo + UI + Cameras)"
echo "2. Gazebo Only"
echo "3. UI Only (requires Gazebo running separately)"
echo "4. Build Docker Image Only"
echo "5. Interactive Shell (for debugging)"
echo

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "Starting complete turret simulation..."
        docker-compose up turret-simulation
        ;;
    2)
        echo "Starting Gazebo simulation only..."
        docker-compose up turret-gazebo-only
        ;;
    3)
        echo "Starting UI only..."
        docker-compose up turret-ui-only
        ;;
    4)
        echo "Building Docker image..."
        docker-compose build
        echo "Build complete!"
        ;;
    5)
        echo "Starting interactive shell..."
        docker-compose run --rm turret-simulation bash
        ;;
    *)
        echo "Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo
echo "Simulation ended."
