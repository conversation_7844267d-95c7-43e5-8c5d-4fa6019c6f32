#!/usr/bin/env python3

import rospy
import cv2
import numpy as np
from sensor_msgs.msg import Image, CameraInfo
from std_msgs.msg import String, Bool, Int32
from cv_bridge import CvBridge, CvBridgeError
import threading

class CameraManager:
    def __init__(self):
        rospy.init_node('camera_manager', anonymous=True)
        
        # CV Bridge
        self.bridge = CvBridge()
        
        # Camera subscribers
        self.main_cam_sub = rospy.Subscriber('/turret/main_camera/image_raw', Image, self.main_camera_callback)
        self.thermal_cam_sub = rospy.Subscriber('/turret/thermal_camera/image_raw', Image, self.thermal_camera_callback)
        
        # Camera info subscribers
        self.main_info_sub = rospy.Subscriber('/turret/main_camera/camera_info', CameraInfo, self.main_info_callback)
        self.thermal_info_sub = rospy.Subscriber('/turret/thermal_camera/camera_info', CameraInfo, self.thermal_info_callback)
        
        # Publishers for processed images
        self.main_processed_pub = rospy.Publisher('/turret/main_camera/processed', Image, queue_size=1)
        self.thermal_processed_pub = rospy.Publisher('/turret/thermal_camera/processed', Image, queue_size=1)
        self.combined_pub = rospy.Publisher('/turret/combined_view', Image, queue_size=1)
        
        # Detection publishers
        self.detection_pub = rospy.Publisher('/turret/detections', String, queue_size=1)
        self.target_count_pub = rospy.Publisher('/turret/target_count', Int32, queue_size=1)
        
        # Control subscribers
        self.processing_mode_sub = rospy.Subscriber('/turret/processing_mode', String, self.mode_callback)
        self.enable_detection_sub = rospy.Subscriber('/turret/enable_detection', Bool, self.detection_enable_callback)
        
        # Camera parameters
        self.main_camera_info = None
        self.thermal_camera_info = None
        
        # Processing parameters
        self.processing_mode = "normal"  # normal, enhanced, thermal_overlay
        self.detection_enabled = True
        self.target_detection_threshold = 0.3
        
        # Image storage
        self.latest_main_image = None
        self.latest_thermal_image = None
        self.image_lock = threading.Lock()
        
        rospy.loginfo("Camera Manager initialized")
        
    def main_camera_callback(self, msg):
        """Process main camera feed"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            with self.image_lock:
                self.latest_main_image = cv_image.copy()
            
            # Process image based on mode
            processed_image = self.process_main_image(cv_image)
            
            # Publish processed image
            try:
                processed_msg = self.bridge.cv2_to_imgmsg(processed_image, "bgr8")
                processed_msg.header = msg.header
                self.main_processed_pub.publish(processed_msg)
            except CvBridgeError as e:
                rospy.logwarn(f"Failed to publish processed main image: {e}")
                
            # Perform detection if enabled
            if self.detection_enabled:
                self.detect_targets(cv_image, "main")
                
        except CvBridgeError as e:
            rospy.logwarn(f"Main camera processing error: {e}")
            
    def thermal_camera_callback(self, msg):
        """Process thermal camera feed"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "mono8")
            
            with self.image_lock:
                self.latest_thermal_image = cv_image.copy()
            
            # Process thermal image
            processed_image = self.process_thermal_image(cv_image)
            
            # Convert back to 3-channel for publishing
            if len(processed_image.shape) == 2:
                processed_image = cv2.cvtColor(processed_image, cv2.COLOR_GRAY2BGR)
            
            # Publish processed image
            try:
                processed_msg = self.bridge.cv2_to_imgmsg(processed_image, "bgr8")
                processed_msg.header = msg.header
                self.thermal_processed_pub.publish(processed_msg)
            except CvBridgeError as e:
                rospy.logwarn(f"Failed to publish processed thermal image: {e}")
                
            # Perform thermal detection if enabled
            if self.detection_enabled:
                self.detect_thermal_targets(cv_image)
                
            # Create combined view
            self.create_combined_view()
                
        except CvBridgeError as e:
            rospy.logwarn(f"Thermal camera processing error: {e}")
            
    def main_info_callback(self, msg):
        """Store main camera info"""
        self.main_camera_info = msg
        
    def thermal_info_callback(self, msg):
        """Store thermal camera info"""
        self.thermal_camera_info = msg
        
    def mode_callback(self, msg):
        """Change processing mode"""
        mode = msg.data.lower()
        if mode in ["normal", "enhanced", "thermal_overlay"]:
            self.processing_mode = mode
            rospy.loginfo(f"Processing mode changed to: {mode}")
            
    def detection_enable_callback(self, msg):
        """Enable/disable detection"""
        self.detection_enabled = msg.data
        rospy.loginfo(f"Detection {'enabled' if self.detection_enabled else 'disabled'}")
        
    def process_main_image(self, image):
        """Process main camera image based on mode"""
        if self.processing_mode == "normal":
            return image
            
        elif self.processing_mode == "enhanced":
            # Apply image enhancement
            enhanced = cv2.convertScaleAbs(image, alpha=1.2, beta=10)
            
            # Apply sharpening
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            return sharpened
            
        elif self.processing_mode == "thermal_overlay":
            # Overlay thermal data if available
            with self.image_lock:
                if self.latest_thermal_image is not None:
                    return self.overlay_thermal(image, self.latest_thermal_image)
            return image
            
        return image
        
    def process_thermal_image(self, image):
        """Process thermal camera image"""
        # Apply colormap for better visualization
        colored = cv2.applyColorMap(image, cv2.COLORMAP_JET)
        
        # Apply histogram equalization
        gray = cv2.cvtColor(colored, cv2.COLOR_BGR2GRAY)
        equalized = cv2.equalizeHist(gray)
        colored_eq = cv2.applyColorMap(equalized, cv2.COLORMAP_JET)
        
        return colored_eq
        
    def overlay_thermal(self, main_image, thermal_image):
        """Overlay thermal image on main image"""
        # Resize thermal to match main image
        thermal_resized = cv2.resize(thermal_image, (main_image.shape[1], main_image.shape[0]))
        
        # Convert thermal to color
        thermal_colored = cv2.applyColorMap(thermal_resized, cv2.COLORMAP_JET)
        
        # Create overlay
        overlay = cv2.addWeighted(main_image, 0.7, thermal_colored, 0.3, 0)
        
        return overlay
        
    def detect_targets(self, image, camera_type):
        """Detect targets in main camera image"""
        # Convert to HSV for color detection
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define color ranges for different targets
        # Red targets
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask_red = mask_red1 + mask_red2
        
        # Blue targets
        lower_blue = np.array([100, 50, 50])
        upper_blue = np.array([130, 255, 255])
        mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)
        
        # Find contours
        targets = []
        
        for color, mask in [("red", mask_red), ("blue", mask_blue)]:
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 500:  # Minimum area threshold
                    # Calculate bounding box
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Calculate centroid
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        targets.append({
                            "color": color,
                            "centroid": (cx, cy),
                            "bbox": (x, y, w, h),
                            "area": area,
                            "camera": camera_type
                        })
        
        # Publish detection results
        if targets:
            detection_msg = f"Detected {len(targets)} targets: "
            for target in targets:
                detection_msg += f"{target['color']} at ({target['centroid'][0]}, {target['centroid'][1]}), "
            
            self.detection_pub.publish(String(data=detection_msg))
            self.target_count_pub.publish(Int32(data=len(targets)))
            
    def detect_thermal_targets(self, thermal_image):
        """Detect hot targets in thermal image"""
        # Find hot spots (bright areas in thermal image)
        _, thresh = cv2.threshold(thermal_image, 200, 255, cv2.THRESH_BINARY)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        hot_targets = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # Minimum area for thermal targets
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    hot_targets.append({
                        "type": "thermal",
                        "centroid": (cx, cy),
                        "area": area,
                        "camera": "thermal"
                    })
        
        if hot_targets:
            thermal_msg = f"Thermal: {len(hot_targets)} hot targets detected"
            self.detection_pub.publish(String(data=thermal_msg))
            
    def create_combined_view(self):
        """Create combined view of both cameras"""
        with self.image_lock:
            if self.latest_main_image is not None and self.latest_thermal_image is not None:
                # Resize images to same height
                height = min(self.latest_main_image.shape[0], 400)
                
                main_resized = cv2.resize(self.latest_main_image, 
                                        (int(self.latest_main_image.shape[1] * height / self.latest_main_image.shape[0]), height))
                
                thermal_colored = cv2.applyColorMap(self.latest_thermal_image, cv2.COLORMAP_JET)
                thermal_resized = cv2.resize(thermal_colored, 
                                           (int(thermal_colored.shape[1] * height / thermal_colored.shape[0]), height))
                
                # Combine horizontally
                combined = np.hstack((main_resized, thermal_resized))
                
                # Add labels
                cv2.putText(combined, "Main Camera", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.putText(combined, "Thermal Camera", (main_resized.shape[1] + 10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                # Publish combined view
                try:
                    combined_msg = self.bridge.cv2_to_imgmsg(combined, "bgr8")
                    self.combined_pub.publish(combined_msg)
                except CvBridgeError as e:
                    rospy.logwarn(f"Failed to publish combined view: {e}")

if __name__ == '__main__':
    try:
        camera_manager = CameraManager()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
