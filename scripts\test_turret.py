#!/usr/bin/env python3

import rospy
from std_msgs.msg import Float64, String
from geometry_msgs.msg import Twist
import math
import time

def test_turret_basic():
    """Basic test of turret functionality"""
    rospy.init_node('turret_test', anonymous=True)
    
    # Publishers
    rotation_pub = rospy.Publisher('/turret/turret_rotation_controller/command', Float64, queue_size=1)
    elevation_pub = rospy.Publisher('/turret/gun_elevation_controller/command', Float64, queue_size=1)
    mode_pub = rospy.Publisher('/turret/set_mode', String, queue_size=1)
    
    rospy.loginfo("Starting turret test sequence...")
    
    # Wait for publishers to connect
    time.sleep(2)
    
    # Test 1: Set manual mode
    rospy.loginfo("Test 1: Setting manual mode")
    mode_pub.publish(String(data="manual"))
    time.sleep(1)
    
    # Test 2: Rotate turret
    rospy.loginfo("Test 2: Rotating turret to 45 degrees")
    rotation_pub.publish(Float64(data=math.radians(45)))
    time.sleep(3)
    
    # Test 3: Elevate gun
    rospy.loginfo("Test 3: Elevating gun to 20 degrees")
    elevation_pub.publish(Float64(data=math.radians(20)))
    time.sleep(3)
    
    # Test 4: Return to center
    rospy.loginfo("Test 4: Returning to center position")
    rotation_pub.publish(Float64(data=0.0))
    elevation_pub.publish(Float64(data=0.0))
    time.sleep(3)
    
    # Test 5: Test patrol mode
    rospy.loginfo("Test 5: Switching to patrol mode")
    mode_pub.publish(String(data="patrol"))
    time.sleep(10)
    
    # Test 6: Return to manual
    rospy.loginfo("Test 6: Returning to manual mode")
    mode_pub.publish(String(data="manual"))
    rotation_pub.publish(Float64(data=0.0))
    elevation_pub.publish(Float64(data=0.0))
    
    rospy.loginfo("Turret test sequence completed!")

if __name__ == '__main__':
    try:
        test_turret_basic()
    except rospy.ROSInterruptException:
        pass
