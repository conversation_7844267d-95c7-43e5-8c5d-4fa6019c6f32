#!/usr/bin/env python3

import rospy
import math
from std_msgs.msg import Float64, String, Bool
from sensor_msgs.msg import JointState, Image, CameraInfo
from geometry_msgs.msg import Twist
from cv_bridge import CvBridge
import cv2
import numpy as np

class TurretController:
    def __init__(self):
        rospy.init_node('turret_controller', anonymous=True)
        
        # Publishers for joint control
        self.rotation_pub = rospy.Publisher('/turret/turret_rotation_controller/command', Float64, queue_size=1)
        self.elevation_pub = rospy.Publisher('/turret/gun_elevation_controller/command', Float64, queue_size=1)
        
        # Publishers for status
        self.status_pub = rospy.Publisher('/turret/status', String, queue_size=1)
        self.mode_pub = rospy.Publisher('/turret/mode', String, queue_size=1)
        
        # Subscribers
        self.joint_sub = rospy.Subscriber('/turret/joint_states', JointState, self.joint_callback)
        self.cmd_sub = rospy.Subscriber('/turret/cmd_vel', Twist, self.cmd_callback)
        self.mode_sub = rospy.Subscriber('/turret/set_mode', String, self.mode_callback)
        self.target_sub = rospy.Subscriber('/turret/target_position', Twist, self.target_callback)
        
        # Camera subscribers
        self.main_cam_sub = rospy.Subscriber('/turret/main_camera/image_raw', Image, self.main_camera_callback)
        self.thermal_cam_sub = rospy.Subscriber('/turret/thermal_camera/image_raw', Image, self.thermal_camera_callback)
        
        # CV Bridge for image processing
        self.bridge = CvBridge()
        
        # Current state
        self.current_rotation = 0.0
        self.current_elevation = 0.0
        self.target_rotation = 0.0
        self.target_elevation = 0.0
        
        # Control parameters
        self.rotation_min = -math.pi
        self.rotation_max = math.pi
        self.elevation_min = -0.5
        self.elevation_max = 0.8
        
        # Control mode
        self.control_mode = "manual"  # manual, auto_track, patrol
        self.patrol_waypoints = [
            {"rotation": 0.0, "elevation": 0.0, "duration": 3.0},
            {"rotation": math.pi/2, "elevation": 0.2, "duration": 3.0},
            {"rotation": math.pi, "elevation": 0.0, "duration": 3.0},
            {"rotation": -math.pi/2, "elevation": 0.2, "duration": 3.0}
        ]
        self.current_waypoint = 0
        self.waypoint_start_time = rospy.Time.now()
        
        # Tracking parameters
        self.tracking_enabled = False
        self.last_detection_time = rospy.Time.now()
        
        rospy.loginfo("Turret Controller initialized")
        
    def joint_callback(self, msg):
        """Update current joint positions"""
        try:
            rotation_idx = msg.name.index('turret_rotation')
            elevation_idx = msg.name.index('gun_elevation')
            
            self.current_rotation = msg.position[rotation_idx]
            self.current_elevation = msg.position[elevation_idx]
            
        except ValueError:
            pass  # Joint not found in message
            
    def cmd_callback(self, msg):
        """Handle manual control commands"""
        if self.control_mode == "manual":
            # Convert twist to target positions
            rotation_delta = msg.angular.z * 0.1  # Scale factor
            elevation_delta = msg.linear.x * 0.1
            
            self.target_rotation = self.clamp_rotation(self.current_rotation + rotation_delta)
            self.target_elevation = self.clamp_elevation(self.current_elevation + elevation_delta)
            
            self.send_commands()
            
    def target_callback(self, msg):
        """Handle direct target position commands"""
        self.target_rotation = self.clamp_rotation(msg.angular.z)
        self.target_elevation = self.clamp_elevation(msg.linear.x)
        self.send_commands()
        
    def mode_callback(self, msg):
        """Handle mode change commands"""
        new_mode = msg.data.lower()
        if new_mode in ["manual", "auto_track", "patrol"]:
            self.control_mode = new_mode
            rospy.loginfo(f"Control mode changed to: {self.control_mode}")
            
            if new_mode == "patrol":
                self.current_waypoint = 0
                self.waypoint_start_time = rospy.Time.now()
                
        self.mode_pub.publish(String(data=self.control_mode))
        
    def main_camera_callback(self, msg):
        """Process main camera feed"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            # Simple object detection (red objects)
            if self.control_mode == "auto_track":
                self.detect_and_track(cv_image)
                
        except Exception as e:
            rospy.logwarn(f"Main camera processing error: {e}")
            
    def thermal_camera_callback(self, msg):
        """Process thermal camera feed"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "mono8")
            # Thermal processing can be added here
            
        except Exception as e:
            rospy.logwarn(f"Thermal camera processing error: {e}")
            
    def detect_and_track(self, image):
        """Simple color-based tracking"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define range for red color
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = mask1 + mask2
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Find largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            if cv2.contourArea(largest_contour) > 500:  # Minimum area threshold
                # Calculate centroid
                M = cv2.moments(largest_contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # Convert pixel coordinates to turret commands
                    image_center_x = image.shape[1] / 2
                    image_center_y = image.shape[0] / 2
                    
                    error_x = cx - image_center_x
                    error_y = cy - image_center_y
                    
                    # Simple proportional control
                    rotation_adjustment = -error_x * 0.001
                    elevation_adjustment = error_y * 0.001
                    
                    self.target_rotation = self.clamp_rotation(self.current_rotation + rotation_adjustment)
                    self.target_elevation = self.clamp_elevation(self.current_elevation + elevation_adjustment)
                    
                    self.send_commands()
                    self.last_detection_time = rospy.Time.now()
                    
    def patrol_mode(self):
        """Execute patrol pattern"""
        if self.control_mode == "patrol":
            current_time = rospy.Time.now()
            elapsed = (current_time - self.waypoint_start_time).to_sec()
            
            current_wp = self.patrol_waypoints[self.current_waypoint]
            
            if elapsed >= current_wp["duration"]:
                # Move to next waypoint
                self.current_waypoint = (self.current_waypoint + 1) % len(self.patrol_waypoints)
                self.waypoint_start_time = current_time
                current_wp = self.patrol_waypoints[self.current_waypoint]
                
            self.target_rotation = current_wp["rotation"]
            self.target_elevation = current_wp["elevation"]
            self.send_commands()
            
    def clamp_rotation(self, value):
        """Clamp rotation to valid range"""
        return max(self.rotation_min, min(self.rotation_max, value))
        
    def clamp_elevation(self, value):
        """Clamp elevation to valid range"""
        return max(self.elevation_min, min(self.elevation_max, value))
        
    def send_commands(self):
        """Send position commands to controllers"""
        self.rotation_pub.publish(Float64(data=self.target_rotation))
        self.elevation_pub.publish(Float64(data=self.target_elevation))
        
    def publish_status(self):
        """Publish current status"""
        status_msg = f"Mode: {self.control_mode}, Rot: {self.current_rotation:.2f}, Elev: {self.current_elevation:.2f}"
        self.status_pub.publish(String(data=status_msg))
        
    def run(self):
        """Main control loop"""
        rate = rospy.Rate(20)  # 20 Hz
        
        while not rospy.is_shutdown():
            if self.control_mode == "patrol":
                self.patrol_mode()
                
            self.publish_status()
            rate.sleep()

if __name__ == '__main__':
    try:
        controller = TurretController()
        controller.run()
    except rospy.ROSInterruptException:
        pass
