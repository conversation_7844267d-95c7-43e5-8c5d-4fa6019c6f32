#!/usr/bin/env python3

import rospy
import tkinter as tk
from tkinter import ttk, messagebox
from std_msgs.msg import String, Float64
from geometry_msgs.msg import Twist
from sensor_msgs.msg import JointState, Image
import threading
import math

class TurretUI:
    def __init__(self):
        rospy.init_node('turret_ui', anonymous=True)
        
        # Publishers
        self.cmd_pub = rospy.Publisher('/turret/cmd_vel', Twist, queue_size=1)
        self.mode_pub = rospy.Publisher('/turret/set_mode', String, queue_size=1)
        self.target_pub = rospy.Publisher('/turret/target_position', Twist, queue_size=1)
        
        # Subscribers
        self.joint_sub = rospy.Subscriber('/turret/joint_states', JointState, self.joint_callback)
        self.status_sub = rospy.Subscriber('/turret/status', String, self.status_callback)
        
        # Current state
        self.current_rotation = 0.0
        self.current_elevation = 0.0
        self.current_mode = "manual"
        self.status_text = "Initializing..."
        
        # Create GUI
        self.create_gui()
        
        # Start ROS spinning in separate thread
        self.ros_thread = threading.Thread(target=self.ros_spin)
        self.ros_thread.daemon = True
        self.ros_thread.start()
        
    def create_gui(self):
        """Create the main GUI window"""
        self.root = tk.Tk()
        self.root.title("Military Turret Control System")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="System Status", padding="10")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.status_label = ttk.Label(status_frame, text=self.status_text, font=('Arial', 10))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Position display
        pos_frame = ttk.LabelFrame(main_frame, text="Current Position", padding="10")
        pos_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(pos_frame, text="Rotation:").grid(row=0, column=0, sticky=tk.W)
        self.rotation_var = tk.StringVar(value="0.00°")
        ttk.Label(pos_frame, textvariable=self.rotation_var, font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=10)
        
        ttk.Label(pos_frame, text="Elevation:").grid(row=1, column=0, sticky=tk.W)
        self.elevation_var = tk.StringVar(value="0.00°")
        ttk.Label(pos_frame, textvariable=self.elevation_var, font=('Arial', 12, 'bold')).grid(row=1, column=1, sticky=tk.W, padx=10)
        
        # Control mode frame
        mode_frame = ttk.LabelFrame(main_frame, text="Control Mode", padding="10")
        mode_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.mode_var = tk.StringVar(value="manual")
        modes = [("Manual", "manual"), ("Auto Track", "auto_track"), ("Patrol", "patrol")]
        
        for i, (text, value) in enumerate(modes):
            ttk.Radiobutton(mode_frame, text=text, variable=self.mode_var, 
                           value=value, command=self.change_mode).grid(row=0, column=i, padx=10)
        
        # Manual control frame
        manual_frame = ttk.LabelFrame(main_frame, text="Manual Control", padding="10")
        manual_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(0, 5))
        
        # Direction buttons
        ttk.Button(manual_frame, text="↑", command=lambda: self.move_turret(0, 1)).grid(row=0, column=1, pady=2)
        ttk.Button(manual_frame, text="←", command=lambda: self.move_turret(-1, 0)).grid(row=1, column=0, padx=2)
        ttk.Button(manual_frame, text="STOP", command=self.stop_turret).grid(row=1, column=1, padx=2, pady=2)
        ttk.Button(manual_frame, text="→", command=lambda: self.move_turret(1, 0)).grid(row=1, column=2, padx=2)
        ttk.Button(manual_frame, text="↓", command=lambda: self.move_turret(0, -1)).grid(row=2, column=1, pady=2)
        
        # Speed control
        ttk.Label(manual_frame, text="Speed:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.speed_var = tk.DoubleVar(value=0.5)
        speed_scale = ttk.Scale(manual_frame, from_=0.1, to=2.0, variable=self.speed_var, orient=tk.HORIZONTAL)
        speed_scale.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Direct position control frame
        direct_frame = ttk.LabelFrame(main_frame, text="Direct Position Control", padding="10")
        direct_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(5, 0))
        
        ttk.Label(direct_frame, text="Target Rotation (°):").grid(row=0, column=0, sticky=tk.W)
        self.target_rot_var = tk.DoubleVar(value=0.0)
        rot_spinbox = ttk.Spinbox(direct_frame, from_=-180, to=180, textvariable=self.target_rot_var, width=10)
        rot_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(direct_frame, text="Target Elevation (°):").grid(row=1, column=0, sticky=tk.W)
        self.target_elev_var = tk.DoubleVar(value=0.0)
        elev_spinbox = ttk.Spinbox(direct_frame, from_=-30, to=45, textvariable=self.target_elev_var, width=10)
        elev_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5)
        
        ttk.Button(direct_frame, text="Go To Position", command=self.go_to_position).grid(row=2, column=0, columnspan=2, pady=10)
        
        # Emergency controls
        emergency_frame = ttk.LabelFrame(main_frame, text="Emergency Controls", padding="10")
        emergency_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(emergency_frame, text="EMERGENCY STOP", command=self.emergency_stop, 
                  style='Emergency.TButton').grid(row=0, column=0, padx=5)
        ttk.Button(emergency_frame, text="Home Position", command=self.home_position).grid(row=0, column=1, padx=5)
        
        # Configure emergency button style
        style = ttk.Style()
        style.configure('Emergency.TButton', foreground='red', font=('Arial', 12, 'bold'))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
    def joint_callback(self, msg):
        """Update current joint positions"""
        try:
            rotation_idx = msg.name.index('turret_rotation')
            elevation_idx = msg.name.index('gun_elevation')
            
            self.current_rotation = msg.position[rotation_idx]
            self.current_elevation = msg.position[elevation_idx]
            
            # Update GUI
            self.rotation_var.set(f"{math.degrees(self.current_rotation):.1f}°")
            self.elevation_var.set(f"{math.degrees(self.current_elevation):.1f}°")
            
        except (ValueError, IndexError):
            pass
            
    def status_callback(self, msg):
        """Update status display"""
        self.status_text = msg.data
        self.status_label.config(text=self.status_text)
        
    def change_mode(self):
        """Change control mode"""
        mode = self.mode_var.get()
        self.mode_pub.publish(String(data=mode))
        rospy.loginfo(f"Mode changed to: {mode}")
        
    def move_turret(self, rotation_dir, elevation_dir):
        """Send movement command"""
        if self.mode_var.get() == "manual":
            cmd = Twist()
            speed = self.speed_var.get()
            cmd.angular.z = rotation_dir * speed
            cmd.linear.x = elevation_dir * speed
            self.cmd_pub.publish(cmd)
            
    def stop_turret(self):
        """Stop turret movement"""
        cmd = Twist()
        self.cmd_pub.publish(cmd)
        
    def go_to_position(self):
        """Go to specified position"""
        target = Twist()
        target.angular.z = math.radians(self.target_rot_var.get())
        target.linear.x = math.radians(self.target_elev_var.get())
        self.target_pub.publish(target)
        
    def emergency_stop(self):
        """Emergency stop"""
        self.stop_turret()
        self.mode_pub.publish(String(data="manual"))
        messagebox.showwarning("Emergency Stop", "Turret stopped and switched to manual mode!")
        
    def home_position(self):
        """Return to home position"""
        target = Twist()
        target.angular.z = 0.0
        target.linear.x = 0.0
        self.target_pub.publish(target)
        
    def ros_spin(self):
        """ROS spinning thread"""
        rospy.spin()
        
    def run(self):
        """Start the GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            rospy.signal_shutdown("GUI closed")

if __name__ == '__main__':
    try:
        ui = TurretUI()
        ui.run()
    except rospy.ROSInterruptException:
        pass
