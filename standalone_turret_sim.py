#!/usr/bin/env python3
"""
Standalone Turret Simulation (No ROS Required)
A simplified version that can run immediately on Windows
"""

import tkinter as tk
from tkinter import ttk, messagebox
import math
import time
import threading
import numpy as np
import cv2
from PIL import Image, ImageTk

class StandaloneTurretSim:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Military Turret Simulation - Standalone")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # Turret state
        self.rotation = 0.0  # degrees
        self.elevation = 0.0  # degrees
        self.target_rotation = 0.0
        self.target_elevation = 0.0
        
        # Control parameters
        self.rotation_min = -180
        self.rotation_max = 180
        self.elevation_min = -30
        self.elevation_max = 45
        self.movement_speed = 1.0
        
        # Control mode
        self.control_mode = "manual"  # manual, patrol, auto_track
        self.patrol_active = False
        self.patrol_waypoints = [
            {"rotation": 0, "elevation": 0, "duration": 3},
            {"rotation": 90, "elevation": 15, "duration": 3},
            {"rotation": 180, "elevation": 0, "duration": 3},
            {"rotation": -90, "elevation": 15, "duration": 3}
        ]
        self.current_waypoint = 0
        self.waypoint_start_time = time.time()
        
        # Camera simulation
        self.camera_active = True
        self.detection_enabled = True
        self.targets_detected = 0
        
        self.create_gui()
        self.start_simulation_thread()
        
    def create_gui(self):
        """Create the main GUI"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = tk.Label(main_frame, text="Military Turret Simulation", 
                              font=('Arial', 16, 'bold'), bg='#2c3e50', fg='white')
        title_label.grid(row=0, column=0, columnspan=3, pady=10)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="System Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.status_text = tk.StringVar(value="System Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_text, font=('Arial', 10))
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Position display
        pos_frame = ttk.LabelFrame(main_frame, text="Current Position", padding="10")
        pos_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(pos_frame, text="Rotation:").grid(row=0, column=0, sticky=tk.W)
        self.rotation_var = tk.StringVar(value="0.0°")
        ttk.Label(pos_frame, textvariable=self.rotation_var, font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=10)
        
        ttk.Label(pos_frame, text="Elevation:").grid(row=0, column=2, sticky=tk.W, padx=20)
        self.elevation_var = tk.StringVar(value="0.0°")
        ttk.Label(pos_frame, textvariable=self.elevation_var, font=('Arial', 12, 'bold')).grid(row=0, column=3, sticky=tk.W, padx=10)
        
        ttk.Label(pos_frame, text="Targets:").grid(row=0, column=4, sticky=tk.W, padx=20)
        self.targets_var = tk.StringVar(value="0")
        ttk.Label(pos_frame, textvariable=self.targets_var, font=('Arial', 12, 'bold'), foreground='red').grid(row=0, column=5, sticky=tk.W, padx=10)
        
        # Control mode frame
        mode_frame = ttk.LabelFrame(main_frame, text="Control Mode", padding="10")
        mode_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.mode_var = tk.StringVar(value="manual")
        modes = [("Manual", "manual"), ("Patrol", "patrol"), ("Auto Track", "auto_track")]
        
        for i, (text, value) in enumerate(modes):
            ttk.Radiobutton(mode_frame, text=text, variable=self.mode_var, 
                           value=value, command=self.change_mode).grid(row=0, column=i, padx=20)
        
        # Left panel - Manual control
        left_frame = ttk.LabelFrame(main_frame, text="Manual Control", padding="10")
        left_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(0, 5))
        
        # Direction buttons
        ttk.Button(left_frame, text="↑ Elevate", command=lambda: self.manual_move(0, 5)).grid(row=0, column=1, pady=2)
        ttk.Button(left_frame, text="← Rotate Left", command=lambda: self.manual_move(-10, 0)).grid(row=1, column=0, padx=2)
        ttk.Button(left_frame, text="STOP", command=self.stop_turret, 
                  style='Emergency.TButton').grid(row=1, column=1, padx=2, pady=2)
        ttk.Button(left_frame, text="Rotate Right →", command=lambda: self.manual_move(10, 0)).grid(row=1, column=2, padx=2)
        ttk.Button(left_frame, text="↓ Depress", command=lambda: self.manual_move(0, -5)).grid(row=2, column=1, pady=2)
        
        # Speed control
        ttk.Label(left_frame, text="Speed:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.speed_var = tk.DoubleVar(value=1.0)
        speed_scale = ttk.Scale(left_frame, from_=0.1, to=3.0, variable=self.speed_var, orient=tk.HORIZONTAL)
        speed_scale.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Middle panel - Visual display
        visual_frame = ttk.LabelFrame(main_frame, text="Turret Visualization", padding="10")
        visual_frame.grid(row=4, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=5)
        
        # Canvas for turret visualization
        self.canvas = tk.Canvas(visual_frame, width=300, height=300, bg='black')
        self.canvas.grid(row=0, column=0, pady=10)
        
        # Camera simulation
        camera_frame = ttk.LabelFrame(visual_frame, text="Camera Feed", padding="5")
        camera_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        
        self.camera_canvas = tk.Canvas(camera_frame, width=280, height=160, bg='gray')
        self.camera_canvas.grid(row=0, column=0)
        
        # Right panel - Direct control
        right_frame = ttk.LabelFrame(main_frame, text="Direct Position Control", padding="10")
        right_frame.grid(row=4, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(5, 0))
        
        ttk.Label(right_frame, text="Target Rotation (°):").grid(row=0, column=0, sticky=tk.W)
        self.target_rot_var = tk.DoubleVar(value=0.0)
        rot_spinbox = ttk.Spinbox(right_frame, from_=-180, to=180, textvariable=self.target_rot_var, width=10)
        rot_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(right_frame, text="Target Elevation (°):").grid(row=1, column=0, sticky=tk.W)
        self.target_elev_var = tk.DoubleVar(value=0.0)
        elev_spinbox = ttk.Spinbox(right_frame, from_=-30, to=45, textvariable=self.target_elev_var, width=10)
        elev_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5)
        
        ttk.Button(right_frame, text="Go To Position", command=self.go_to_position).grid(row=2, column=0, columnspan=2, pady=10)
        
        # Patrol controls
        patrol_frame = ttk.LabelFrame(right_frame, text="Patrol Settings", padding="5")
        patrol_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Button(patrol_frame, text="Start Patrol", command=self.start_patrol).grid(row=0, column=0, padx=2)
        ttk.Button(patrol_frame, text="Stop Patrol", command=self.stop_patrol).grid(row=0, column=1, padx=2)
        
        # Detection controls
        detection_frame = ttk.LabelFrame(right_frame, text="Detection", padding="5")
        detection_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        self.detection_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(detection_frame, text="Enable Detection", variable=self.detection_var,
                       command=self.toggle_detection).grid(row=0, column=0, columnspan=2)
        
        # Emergency controls
        emergency_frame = ttk.LabelFrame(main_frame, text="Emergency Controls", padding="10")
        emergency_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(emergency_frame, text="EMERGENCY STOP", command=self.emergency_stop, 
                  style='Emergency.TButton').grid(row=0, column=0, padx=5)
        ttk.Button(emergency_frame, text="Home Position", command=self.home_position).grid(row=0, column=1, padx=5)
        ttk.Button(emergency_frame, text="System Reset", command=self.system_reset).grid(row=0, column=2, padx=5)
        
        # Configure emergency button style
        style = ttk.Style()
        style.configure('Emergency.TButton', foreground='red', font=('Arial', 10, 'bold'))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
    def start_simulation_thread(self):
        """Start the simulation update thread"""
        self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
        self.simulation_thread.start()
        
    def simulation_loop(self):
        """Main simulation loop"""
        while True:
            try:
                # Update turret position
                self.update_turret_position()
                
                # Handle patrol mode
                if self.control_mode == "patrol" and self.patrol_active:
                    self.update_patrol()
                
                # Simulate target detection
                if self.detection_enabled:
                    self.simulate_detection()
                
                # Update GUI
                self.root.after(0, self.update_gui)
                
                time.sleep(0.05)  # 20 Hz update rate
                
            except Exception as e:
                print(f"Simulation error: {e}")
                time.sleep(0.1)
                
    def update_turret_position(self):
        """Update turret position towards target"""
        # Rotation
        rot_diff = self.target_rotation - self.rotation
        if abs(rot_diff) > 0.1:
            move_amount = min(abs(rot_diff), self.movement_speed * self.speed_var.get())
            if rot_diff > 0:
                self.rotation += move_amount
            else:
                self.rotation -= move_amount
                
        # Elevation
        elev_diff = self.target_elevation - self.elevation
        if abs(elev_diff) > 0.1:
            move_amount = min(abs(elev_diff), self.movement_speed * self.speed_var.get())
            if elev_diff > 0:
                self.elevation += move_amount
            else:
                self.elevation -= move_amount
                
        # Apply limits
        self.rotation = max(self.rotation_min, min(self.rotation_max, self.rotation))
        self.elevation = max(self.elevation_min, min(self.elevation_max, self.elevation))
        
    def update_patrol(self):
        """Update patrol behavior"""
        current_time = time.time()
        elapsed = current_time - self.waypoint_start_time
        
        current_wp = self.patrol_waypoints[self.current_waypoint]
        
        if elapsed >= current_wp["duration"]:
            # Move to next waypoint
            self.current_waypoint = (self.current_waypoint + 1) % len(self.patrol_waypoints)
            self.waypoint_start_time = current_time
            current_wp = self.patrol_waypoints[self.current_waypoint]
            
        self.target_rotation = current_wp["rotation"]
        self.target_elevation = current_wp["elevation"]
        
    def simulate_detection(self):
        """Simulate target detection"""
        # Simple random detection simulation
        import random
        if random.random() < 0.01:  # 1% chance per update
            self.targets_detected = random.randint(0, 3)
        elif random.random() < 0.05:  # 5% chance to clear
            self.targets_detected = 0
            
    def update_gui(self):
        """Update GUI elements"""
        # Update position displays
        self.rotation_var.set(f"{self.rotation:.1f}°")
        self.elevation_var.set(f"{self.elevation:.1f}°")
        self.targets_var.set(str(self.targets_detected))
        
        # Update status
        status = f"Mode: {self.control_mode.title()}, "
        status += f"Rot: {self.rotation:.1f}°, Elev: {self.elevation:.1f}°"
        if self.patrol_active:
            status += f", Waypoint: {self.current_waypoint + 1}"
        self.status_text.set(status)
        
        # Update turret visualization
        self.draw_turret()
        self.draw_camera_feed()
        
    def draw_turret(self):
        """Draw turret on canvas"""
        self.canvas.delete("all")
        
        # Canvas center
        cx, cy = 150, 150
        
        # Draw base
        self.canvas.create_oval(cx-50, cy-50, cx+50, cy+50, fill='green', outline='darkgreen', width=2)
        
        # Draw turret (rotated)
        rot_rad = math.radians(self.rotation)
        turret_x = cx + 30 * math.cos(rot_rad)
        turret_y = cy + 30 * math.sin(rot_rad)
        
        # Draw gun barrel (with elevation)
        elev_rad = math.radians(self.elevation)
        barrel_length = 40
        barrel_end_x = turret_x + barrel_length * math.cos(rot_rad + elev_rad)
        barrel_end_y = turret_y + barrel_length * math.sin(rot_rad + elev_rad)
        
        self.canvas.create_line(turret_x, turret_y, barrel_end_x, barrel_end_y, 
                               fill='black', width=4)
        
        # Draw turret body
        self.canvas.create_oval(turret_x-15, turret_y-15, turret_x+15, turret_y+15, 
                               fill='darkgreen', outline='black', width=2)
        
        # Draw direction indicator
        self.canvas.create_line(cx, cy, turret_x, turret_y, fill='yellow', width=2)
        
        # Draw angle text
        self.canvas.create_text(10, 10, text=f"Rotation: {self.rotation:.1f}°", 
                               fill='white', anchor='nw')
        self.canvas.create_text(10, 25, text=f"Elevation: {self.elevation:.1f}°", 
                               fill='white', anchor='nw')
        
    def draw_camera_feed(self):
        """Draw simulated camera feed"""
        self.camera_canvas.delete("all")
        
        # Background
        self.camera_canvas.create_rectangle(0, 0, 280, 160, fill='darkblue')
        
        # Crosshairs
        self.camera_canvas.create_line(140, 0, 140, 160, fill='green', width=1)
        self.camera_canvas.create_line(0, 80, 280, 80, fill='green', width=1)
        
        # Simulate targets
        if self.targets_detected > 0:
            import random
            for i in range(self.targets_detected):
                x = random.randint(20, 260)
                y = random.randint(20, 140)
                self.camera_canvas.create_rectangle(x-10, y-10, x+10, y+10, 
                                                  outline='red', width=2)
                self.camera_canvas.create_text(x, y-15, text="TARGET", fill='red', font=('Arial', 8))
        
        # Camera info
        self.camera_canvas.create_text(5, 5, text="MAIN CAM", fill='white', anchor='nw', font=('Arial', 8))
        self.camera_canvas.create_text(5, 150, text=f"TARGETS: {self.targets_detected}", 
                                     fill='red', anchor='sw', font=('Arial', 8))
        
    def change_mode(self):
        """Change control mode"""
        self.control_mode = self.mode_var.get()
        if self.control_mode == "patrol":
            self.start_patrol()
        else:
            self.stop_patrol()
            
    def manual_move(self, rotation_delta, elevation_delta):
        """Manual movement"""
        if self.control_mode == "manual":
            self.target_rotation = max(self.rotation_min, 
                                     min(self.rotation_max, 
                                         self.rotation + rotation_delta))
            self.target_elevation = max(self.elevation_min, 
                                      min(self.elevation_max, 
                                          self.elevation + elevation_delta))
            
    def stop_turret(self):
        """Stop turret movement"""
        self.target_rotation = self.rotation
        self.target_elevation = self.elevation
        
    def go_to_position(self):
        """Go to specified position"""
        self.target_rotation = max(self.rotation_min, 
                                 min(self.rotation_max, self.target_rot_var.get()))
        self.target_elevation = max(self.elevation_min, 
                                  min(self.elevation_max, self.target_elev_var.get()))
        
    def start_patrol(self):
        """Start patrol mode"""
        self.patrol_active = True
        self.current_waypoint = 0
        self.waypoint_start_time = time.time()
        
    def stop_patrol(self):
        """Stop patrol mode"""
        self.patrol_active = False
        
    def toggle_detection(self):
        """Toggle target detection"""
        self.detection_enabled = self.detection_var.get()
        
    def emergency_stop(self):
        """Emergency stop"""
        self.stop_turret()
        self.stop_patrol()
        self.mode_var.set("manual")
        self.control_mode = "manual"
        messagebox.showwarning("Emergency Stop", "Turret stopped and switched to manual mode!")
        
    def home_position(self):
        """Return to home position"""
        self.target_rotation = 0.0
        self.target_elevation = 0.0
        
    def system_reset(self):
        """Reset system to initial state"""
        self.rotation = 0.0
        self.elevation = 0.0
        self.target_rotation = 0.0
        self.target_elevation = 0.0
        self.stop_patrol()
        self.mode_var.set("manual")
        self.control_mode = "manual"
        self.targets_detected = 0
        
    def run(self):
        """Start the simulation"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("Simulation stopped")

if __name__ == '__main__':
    print("Starting Standalone Turret Simulation...")
    print("This simulation runs without ROS and provides basic turret functionality.")
    print("Use the GUI to control the turret in different modes.")
    
    sim = StandaloneTurretSim()
    sim.run()
