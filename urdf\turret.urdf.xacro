<?xml version="1.0"?>
<robot name="military_turret" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- Include common macros -->
  <xacro:include filename="$(find turret_simulation)/urdf/turret_macros.xacro"/>

  <!-- Material definitions -->
  <material name="military_green">
    <color rgba="0.4 0.5 0.3 1.0"/>
  </material>
  
  <material name="dark_gray">
    <color rgba="0.3 0.3 0.3 1.0"/>
  </material>
  
  <material name="black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>

  <!-- World link (required for Gazebo) -->
  <link name="world"/>

  <!-- Fixed Base -->
  <link name="fixed_base">
    <visual>
      <origin xyz="0 0 0.05" rpy="0 0 0"/>
      <geometry>
        <box size="1.0 1.0 0.1"/>
      </geometry>
      <material name="military_green"/>
    </visual>
    <collision>
      <origin xyz="0 0 0.05" rpy="0 0 0"/>
      <geometry>
        <box size="1.0 1.0 0.1"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0.05" rpy="0 0 0"/>
      <mass value="50.0"/>
      <inertia ixx="4.17" ixy="0.0" ixz="0.0" iyy="4.17" iyz="0.0" izz="8.33"/>
    </inertial>
  </link>

  <!-- Joint connecting world to fixed base -->
  <joint name="world_to_base" type="fixed">
    <parent link="world"/>
    <child link="fixed_base"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
  </joint>

  <!-- Rotating Turret Base -->
  <link name="turret_base">
    <visual>
      <origin xyz="0 0 0.15" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.3" length="0.3"/>
      </geometry>
      <material name="military_green"/>
    </visual>
    <collision>
      <origin xyz="0 0 0.15" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.3" length="0.3"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0.15" rpy="0 0 0"/>
      <mass value="20.0"/>
      <inertia ixx="1.95" ixy="0.0" ixz="0.0" iyy="1.95" iyz="0.0" izz="0.9"/>
    </inertial>
  </link>

  <!-- Turret rotation joint (horizontal rotation) -->
  <joint name="turret_rotation" type="continuous">
    <parent link="fixed_base"/>
    <child link="turret_base"/>
    <origin xyz="0 0 0.1" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="0.1" friction="0.1"/>
    <limit effort="100.0" velocity="1.57"/>
  </joint>

  <!-- Gun Mount -->
  <link name="gun_mount">
    <visual>
      <origin xyz="0.2 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.4 0.2 0.15"/>
      </geometry>
      <material name="dark_gray"/>
    </visual>
    <collision>
      <origin xyz="0.2 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.4 0.2 0.15"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0.2 0 0" rpy="0 0 0"/>
      <mass value="15.0"/>
      <inertia ixx="0.44" ixy="0.0" ixz="0.0" iyy="2.44" iyz="0.0" izz="2.06"/>
    </inertial>
  </link>

  <!-- Gun elevation joint (vertical tilt) -->
  <joint name="gun_elevation" type="revolute">
    <parent link="turret_base"/>
    <child link="gun_mount"/>
    <origin xyz="0 0 0.3" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-0.5" upper="0.8" effort="50.0" velocity="1.0"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>

  <!-- Gun Barrel -->
  <link name="gun_barrel">
    <visual>
      <origin xyz="0.5 0 0" rpy="0 1.57 0"/>
      <geometry>
        <cylinder radius="0.03" length="1.0"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0.5 0 0" rpy="0 1.57 0"/>
      <geometry>
        <cylinder radius="0.03" length="1.0"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0.5 0 0" rpy="0 1.57 0"/>
      <mass value="5.0"/>
      <inertia ixx="0.42" ixy="0.0" ixz="0.0" iyy="0.42" iyz="0.0" izz="0.002"/>
    </inertial>
  </link>

  <!-- Gun barrel joint -->
  <joint name="gun_barrel_joint" type="fixed">
    <parent link="gun_mount"/>
    <child link="gun_barrel"/>
    <origin xyz="0.4 0 0" rpy="0 0 0"/>
  </joint>

  <!-- Main Camera -->
  <link name="main_camera">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.08 0.08 0.06"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.08 0.08 0.06"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>

  <!-- Main camera joint -->
  <joint name="main_camera_joint" type="fixed">
    <parent link="gun_mount"/>
    <child link="main_camera"/>
    <origin xyz="0.1 0.15 0.1" rpy="0 0 0"/>
  </joint>

  <!-- Thermal/IR Camera -->
  <link name="thermal_camera">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.06 0.06 0.05"/>
      </geometry>
      <material name="dark_gray"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.06 0.06 0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.3"/>
      <inertia ixx="0.0005" ixy="0.0" ixz="0.0" iyy="0.0005" iyz="0.0" izz="0.0005"/>
    </inertial>
  </link>

  <!-- Thermal camera joint -->
  <joint name="thermal_camera_joint" type="fixed">
    <parent link="gun_mount"/>
    <child link="thermal_camera"/>
    <origin xyz="0.1 -0.15 0.1" rpy="0 0 0"/>
  </joint>

  <!-- Electronic Speed Controller (ESC) -->
  <link name="esc">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 0.08 0.03"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 0.08 0.03"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.2"/>
      <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
    </inertial>
  </link>

  <!-- ESC joint -->
  <joint name="esc_joint" type="fixed">
    <parent link="turret_base"/>
    <child link="esc"/>
    <origin xyz="0.2 0.2 0.2" rpy="0 0 0"/>
  </joint>

  <!-- Main Battery Pack -->
  <link name="battery_pack">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.15 0.1 0.08"/>
      </geometry>
      <material name="dark_gray"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.15 0.1 0.08"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="2.0"/>
      <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>

  <!-- Battery pack joint -->
  <joint name="battery_pack_joint" type="fixed">
    <parent link="turret_base"/>
    <child link="battery_pack"/>
    <origin xyz="-0.2 0.2 0.15" rpy="0 0 0"/>
  </joint>

  <!-- Instantiate camera sensors -->
  <xacro:camera_sensor name="main_camera" parent_link="main_camera"
                       xyz="0 0 0" rpy="0 0 0" width="1920" height="1080"/>

  <xacro:thermal_camera_sensor name="thermal_camera" parent_link="thermal_camera"
                               xyz="0 0 0" rpy="0 0 0" width="640" height="480"/>

  <!-- Transmission for turret rotation -->
  <transmission name="turret_rotation_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="turret_rotation">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="turret_rotation_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>

  <!-- Transmission for gun elevation -->
  <transmission name="gun_elevation_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="gun_elevation">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="gun_elevation_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>

</robot>

