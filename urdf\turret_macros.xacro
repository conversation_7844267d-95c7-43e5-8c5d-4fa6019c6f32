<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- Gazebo plugins and sensors -->
  
  <!-- Camera sensor macro -->
  <xacro:macro name="camera_sensor" params="name parent_link xyz rpy width height">
    <gazebo reference="${name}">
      <sensor type="camera" name="${name}_sensor">
        <update_rate>30.0</update_rate>
        <camera name="${name}">
          <horizontal_fov>1.3962634</horizontal_fov>
          <image>
            <width>${width}</width>
            <height>${height}</height>
            <format>R8G8B8</format>
          </image>
          <clip>
            <near>0.02</near>
            <far>300</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.007</stddev>
          </noise>
        </camera>
        <plugin name="camera_controller" filename="libgazebo_ros_camera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>turret/${name}</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>${name}</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- Thermal camera sensor macro -->
  <xacro:macro name="thermal_camera_sensor" params="name parent_link xyz rpy width height">
    <gazebo reference="${name}">
      <sensor type="camera" name="${name}_sensor">
        <update_rate>15.0</update_rate>
        <camera name="${name}">
          <horizontal_fov>1.0472</horizontal_fov>
          <image>
            <width>${width}</width>
            <height>${height}</height>
            <format>L8</format>
          </image>
          <clip>
            <near>0.02</near>
            <far>300</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.01</stddev>
          </noise>
        </camera>
        <plugin name="thermal_camera_controller" filename="libgazebo_ros_camera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>turret/${name}</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>${name}</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- Joint control plugin -->
  <gazebo>
    <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
      <robotNamespace>/turret</robotNamespace>
      <robotSimType>gazebo_ros_control/DefaultRobotHWSim</robotSimType>
      <legacyModeNS>true</legacyModeNS>
    </plugin>
  </gazebo>

  <!-- Material properties for Gazebo -->
  <gazebo reference="fixed_base">
    <material>Gazebo/Green</material>
    <mu1>0.8</mu1>
    <mu2>0.8</mu2>
  </gazebo>

  <gazebo reference="turret_base">
    <material>Gazebo/Green</material>
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
  </gazebo>

  <gazebo reference="gun_mount">
    <material>Gazebo/Grey</material>
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
  </gazebo>

  <gazebo reference="gun_barrel">
    <material>Gazebo/Black</material>
    <mu1>0.8</mu1>
    <mu2>0.8</mu2>
  </gazebo>

  <gazebo reference="main_camera">
    <material>Gazebo/Black</material>
  </gazebo>

  <gazebo reference="thermal_camera">
    <material>Gazebo/Grey</material>
  </gazebo>

  <gazebo reference="esc">
    <material>Gazebo/Black</material>
  </gazebo>

  <gazebo reference="battery_pack">
    <material>Gazebo/Grey</material>
  </gazebo>

</robot>
